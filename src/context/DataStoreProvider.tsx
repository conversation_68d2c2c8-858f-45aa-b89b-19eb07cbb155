import React, {createContext, useState, useMemo} from 'react';
import {useContextWrapper} from '../hooks';
import {
  CrewMember,
  IdName,
  OfficeItem,
  UserRoleControllerConfig,
  VesselData,
} from '../types';
import {ErrorPage} from '@paris2/styleguide';

export interface IDataStoreContext {
  roleConfig: UserRoleControllerConfig;
  dataStore: {
    riskCategoryList: RiskCategory[] | [];
    hazardsList: RiskCategory[] | [];
    riskParameterType: RiskParameter[] | [];
    riskParameterList: RiskCategory[] | [];
    taskReliabilityAssessList: TaskReliabilityAssessList[] | [];
    riskParameterListForRiskRaiting: RiskCategory[] | [];
    vesselListForRisk: VesselData[] | [];
    officeListForRisk: OfficeItem[] | [];
    approversReqListForRiskOffice: IdName[] | [];
    approversReqListForRiskVessel: IdName[] | [];
    crewMembersListForRisk: CrewMember[] | [];
  };
  setDataStore: React.Dispatch<
    React.SetStateAction<IDataStoreContext['dataStore']>
  >;
  ga4EventTrigger: any;
}

export interface INoNNullableDataStoreContext extends IDataStoreContext {
  dataStore: IDataStoreContext['dataStore'];
}

type DataStoreContextUnion = IDataStoreContext | INoNNullableDataStoreContext;

export type RiskCategory = {
  id: number;
  name: string;
};

export type RiskParameter = {
  id: number;
  name: string;
  parameters: {
    id: number;
    name: string;
  }[];
};
export type TaskReliabilityAssessList = {
  id: number;
  name: string;
  options: string[];
};

const initialState = {
  dataStore: {
    riskCategoryList: [] as RiskCategory[],
    hazardsList: [] as RiskCategory[],
    riskParameterType: [] as RiskParameter[],
    taskReliabilityAssessList: [] as TaskReliabilityAssessList[],
    riskParameterList: [] as RiskCategory[],
    riskParameterListForRiskRaiting: [] as RiskCategory[],
    vesselListForRisk: [] as VesselData[],
    officeListForRisk: [] as OfficeItem[],
    approversReqListForRiskOffice: [] as IdName[],
    approversReqListForRiskVessel: [] as IdName[],
    crewMembersListForRisk: [] as CrewMember[],
  },
};

export const DataStoreContext = createContext<IDataStoreContext | null>(null);

interface Props {
  roleConfig: UserRoleControllerConfig;
  ga4EventTrigger: any;
}

export const useDataStoreContext = <
  T extends DataStoreContextUnion = IDataStoreContext,
>() =>
  useContextWrapper(DataStoreContext, {
    contextName: useDataStoreContext.name,
    providerName: DataStoreProvider.name,
  }) as T;

const DataStoreProvider = (props: React.PropsWithChildren<Props>) => {
  const {roleConfig, ga4EventTrigger} = props;
  const {
    riskAssessment: {hasPermision},
  } = roleConfig;

  const [dataStore, setDataStore] = useState<IDataStoreContext['dataStore']>(
    initialState['dataStore'],
  );

  const value = useMemo(
    () => ({dataStore, setDataStore, roleConfig, ga4EventTrigger}),
    [dataStore, roleConfig, ga4EventTrigger],
  );
  if (!hasPermision) {
    return <ErrorPage errorCode={403} />;
  }

  return (
    <DataStoreContext.Provider value={value}>
      {props.children}
    </DataStoreContext.Provider>
  );
};

export default DataStoreProvider;
