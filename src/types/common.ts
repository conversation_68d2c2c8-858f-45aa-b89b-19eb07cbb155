export type CommonOptionResponse = {
  message: string;
  result: {
    id: number;
    name: string;
  }[];
};

export type CommonStringOptionResponse = {
  message: string;
  result: string[];
};

export interface OfficeItem {
  id: number;
  value: string;
  ship_party_id: number;
}

export interface CrewMember {
  seafarer_id: number;
  seafarer_person_id: number;
  seafarer_hkid: number;
  seafarer_rank_id: number;
  seafarer_name: string;
  seafarer_rank: string;
  seafarer_rank_sort_order: number;
}
export interface OfficeApproverAPIResponse {
  id: string;
  first_name: string;
  last_name?: string;
  rank?: string;
  email?: string;
  username?: string;
  full_name?: string;
  attributes?: any;
}
export interface OfficeApprover {
  user_id: string;
  first_name: string;
  last_name?: string;
  rank?: string;
  email?: string;
}
export interface IdName {
  id: number;
  name: string;
}

type ParameterType = {
  id: number;
  name: string;
};

export type RiskParameter = {
  id: number;
  name: string;
  parameter_type: ParameterType;
};

export type GroupedParameter = {
  id: number;
  name: string;
  parameters: {
    id: number;
    name: string;
  }[];
};
