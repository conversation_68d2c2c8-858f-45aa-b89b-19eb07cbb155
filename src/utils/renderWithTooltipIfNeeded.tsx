import React from 'react';
import {OverlayTrigger, Tooltip} from 'react-bootstrap';

/**
 * Returns either the original text or a truncated version wrapped in an OverlayTrigger tooltip.
 *
 * @param text - The full string to render or truncate.
 * @param charLimit - Optional character limit (including 3-dot ellipsis). Defaults to 105.
 * @returns A JSX element with tooltip if text is too long, or plain string otherwise.
 */
export const renderWithTooltipIfNeeded = (
  text: string,
  charLimit: number = 105,
): JSX.Element | string => {
  const ellipsis = '...';

  if (text.length > charLimit) {
    const truncatedText = text.slice(0, charLimit - ellipsis.length);
    return (
      <OverlayTrigger
        placement="bottom"
        overlay={<Tooltip id="info-tooltip">{text}</Tooltip>}
      >
        <span style={{cursor: 'pointer'}}>{truncatedText + ellipsis}</span>
      </OverlayTrigger>
    );
  }

  return text;
};
