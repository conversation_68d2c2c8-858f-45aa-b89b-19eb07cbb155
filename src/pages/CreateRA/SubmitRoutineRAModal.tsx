import React, {useState} from 'react';
import {<PERSON>dal, Button, Form} from 'react-bootstrap';
import {toast} from 'react-toastify';
import CustomDatePicker from '../../components/CustomDatePicker';
import {getErrorMessage} from '../../utils/common';

import '../../styles/components/re-assign-approver-modal.scss';

interface SubmitRoutineRAModalProps {
  onConfirm: (params: {
    approveDate: Date;
  }) => Promise<{message?: string} | void>;
  trigger: React.ReactElement;
}

const SubmitRoutineRAModal: React.FC<SubmitRoutineRAModalProps> = ({
  onConfirm,
  trigger,
}) => {
  const [show, setShow] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [approvalDate, setApprovalDate] = useState<Date | undefined>(undefined);
  const handleSubmit = async (date: Date) => {
    try {
      setIsLoading(true);
      const result = await onConfirm({
        approveDate: date,
      });
      const {message} = result || {};
      if (message) {
        toast.success(message);
      } else {
        toast.success('RA approved successfully.');
      }
    } catch (error) {
      toast.error(
        getErrorMessage(
          error,
          'Failed to approve the RA. Please try again later.',
        ),
      );
    } finally {
      setIsLoading(false);
    }

    handleClose();
  };

  const handleTriggerClick = () => {
    setShow(true);
  };

  const handleClose = () => {
    setShow(false);
    setApprovalDate(undefined);
    setIsLoading(false);
  };

  return (
    <>
      {trigger &&
        React.cloneElement(trigger, {
          onClick: handleTriggerClick,
        })}
      <Modal
        show={show}
        onHide={handleClose}
        size="lg"
        backdrop="static"
        className="reassign-approver-modal"
        dialogClassName="top-modal"
      >
        <Modal.Header>
          <Modal.Title className="fs-20">Approving Routine RA</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <div className="text-warning yellow-text-warning">
            <strong>
              Do you want to set the Level of RA as Routine? Setting it as
              Routine can only be done along with Approving the Risk Assessment.
            </strong>{' '}
            <span style={{fontWeight: 400, fontSize: 14}}>
              After your approval, the Vessel will be notified by sending an
              email.
            </span>
          </div>
          <Form.Group>
            <CustomDatePicker
              isRequired={true}
              minDate={undefined}
              label="Approval Date"
              value={approvalDate}
              onChange={date => setApprovalDate(date)}
              placeholder="Select Date"
              controlId="approval_date"
              errorMsg=""
            />
          </Form.Group>
        </Modal.Body>
        <Modal.Footer>
          <Button
            variant="primary"
            className="me-2 fs-14"
            onClick={handleClose}
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button
            variant="secondary"
            className="me-2 fs-14"
            onClick={() => handleSubmit(approvalDate as unknown as Date)}
            disabled={isLoading || !approvalDate}
          >
            Set & Approve
          </Button>
        </Modal.Footer>
      </Modal>
    </>
  );
};

export default SubmitRoutineRAModal;
