import React, {useEffect, useState} from 'react';
import {useParams} from 'react-router-dom';
import {TemplateForm} from '../../types';
import {createFormFromData, groupRiskParameters} from '../../utils/helper';
import {
  getHazardsList,
  getMainRiskParameterType,
  getRiskCategoryList,
  getRiskParameterType,
  getTaskReliabilityAssessList,
  getTemplateById,
} from '../../services/services';
import Loader from '../../components/Loader';
import PreviewFormDetails from '../CreateRA/PreviewFormDetails';
import {useDataStoreContext} from '../../context';

export default function TemplateView() {
  const {setDataStore} = useDataStoreContext();
  const params = useParams<{id: string}>();
  const id = String(params.id);
  const [isLoading, setIsLoading] = useState(true);
  const [form, setForm] = useState<TemplateForm>(createFormFromData());
  useEffect(() => {
    const loadBasicDetails = async () => {
      try {
        setIsLoading(true);
        const results = await Promise.allSettled([
          getRiskCategoryList(),
          getHazardsList(),
          getRiskParameterType(),
          getTaskReliabilityAssessList(),
          getMainRiskParameterType(),
          getMainRiskParameterType(true),
        ]);

        // Check if any promises were rejected
        const rejectedResults = results.filter(
          result => result.status === 'rejected',
        );
        if (rejectedResults.length > 0) {
          // Log the first rejection reason for debugging
          const firstRejection = rejectedResults[0] as PromiseRejectedResult;
          throw new Error(
            firstRejection.reason?.message || 'Failed to load data',
          );
        }

        const [
          categorListData,
          hazardsListData,
          riskParameterData,
          taskRelAssessData,
          mainRiskParameterData,
          mainRiskParameterDataForRiskRating,
        ] = results.map(result =>
          result.status === 'fulfilled' ? result.value : [],
        );

        const groupedRiskParameterData = groupRiskParameters(riskParameterData);
        setDataStore((prev: any) => ({
          ...prev,
          riskCategoryList: categorListData,
          hazardsList: hazardsListData,
          riskParameterType: groupedRiskParameterData,
          taskReliabilityAssessList: taskRelAssessData,
          riskParameterList: mainRiskParameterData,
          riskParameterListForRiskRaiting: mainRiskParameterDataForRiskRating,
        }));
      } catch (error) {
        console.error('Error loading data:', error);
      } finally {
        setIsLoading(false);
      }
    };
    loadBasicDetails();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  const fetchTemplateData = async (templateId: string) => {
    setIsLoading(true);
    try {
      const response = await getTemplateById(templateId);
      const data = response.result;
      const formData = createFormFromData(data);
      setForm(formData);
    } catch (err) {
      console.error('Error fetching draft', err);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (!id) return;
    fetchTemplateData(id);
  }, [id]);

  return (
    <>
      {isLoading && <Loader isOverlayLoader />}
      <PreviewFormDetails
        form={form}
        setForm={setForm}
        atRiskRef={{current: null}}
        handlePreviewPublush={() => {}}
        handleSaveToDraft={() => {}}
        type="template"
        previewOnly
        showBreadCrumb
        showUseTemplate
        breadcrumbOptions={{
          items: [
            {title: 'Risk Assessment', link: '/risk-assessment'},
            {title: 'Templates', link: '/risk-assessment/template-listing'},
            {title: form?.task_requiring_ra || ''}, // No link, just text
          ],
        }}
      />
    </>
  );
}
