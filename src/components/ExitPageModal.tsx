import React from 'react';
import {<PERSON><PERSON>, But<PERSON>} from 'react-bootstrap';

type Props = {
  onClose: () => void;
  onConfirm: () => void;
};

export const ExitPageModal: React.FC<Props> = ({onClose, onConfirm}) => {
  return (
    <Modal
      show
      onHide={onClose}
      size="lg"
      backdrop="static"
      dialogClassName="top-modal"
    >
      <Modal.Header>
        <Modal.Title className="fs-20">Assign the Approvers</Modal.Title>
      </Modal.Header>
      <Modal.Body className="complete-project-modal">
        <div
          className="alert d-flex align-items-center fs-14 ra-alert-warning"
          role="alert"
          data-testid="error-alert"
        >
          <div>
            <strong>Do you want to exit this page?</strong>
          </div>
        </div>
      </Modal.Body>

      <Modal.Footer>
        <Button variant="primary" className="me-2 fs-14" onClick={onConfirm}>
          Exit Page
        </Button>
        <Button
          variant="secondary"
          className="me-2 fs-14"
          onClick={() => onClose()}
        >
          Assign Approvers
        </Button>
      </Modal.Footer>
    </Modal>
  );
};
