import React, {useEffect, useState} from 'react';
import {Mo<PERSON>, Button} from 'react-bootstrap';
import {getRiskLevel} from '../utils/helper';

export const consequenceRows = [
  {
    label: 'Minor (A)',
    codes: ['A1', 'A2', 'A3', 'A4', 'A5'],
    desc: 'First aid or equivalent',
  },
  {
    label: 'Medium (B)',
    desc: 'Medical treatment injury/ Restricted work injury',
    codes: ['B1', 'B2', 'B3', 'B4', 'B5'],
  },
  {
    label: 'Major (C)',
    codes: ['C1', 'C2', 'C3', 'C4', 'C5'],
    desc: 'Injury Leading to Loss Time Incident (LTI)',
  },
  {
    label: 'Severe (D)',
    desc: 'Permanent or partial disability',
    codes: ['D1', 'D2', 'D3', 'D4', 'D5'],
  },
  {
    label: 'Catastrophic (E)',
    codes: ['E1', 'E2', 'E3', 'E4', 'E5'],
    desc: 'Fatality',
  },
];

export const likelihoodCols = [
  {
    label: 'Rarely (1)',
    desc: 'Unlikely to occur. May occur only in exceptional circumstances once in 25 years',
  },
  {
    label: 'Unlikely (2)',
    desc: 'Not likely to occur but possible can occur in same circumstances once in 10 year',
  },
  {label: 'Likely (3)', desc: 'An event may occur at same time Once in 5 year'},
  {
    label: 'Imminent (4)',
    desc: 'An event will probably occur in many circumstances Once per year',
  },
  {
    label: 'Certain (5)',
    desc: 'It is expected to occur in most circumstances Once per month',
  },
];

const greenCodes = ['A1', 'A2', 'A3', 'B1', 'B2', 'C1'];
const redCodes = ['C5', 'D4', 'D5', 'E3', 'E4', 'E5'];

export const getCellColor = (code: string) => {
  if (greenCodes.includes(code)) return '#28A747'; // green
  if (redCodes.includes(code)) return '#D41B56'; // red
  return '#FFC107'; // yellow
};
const compareRiskCodes = (codeA: string, codeB: string) => {
  // Returns -1 if codeA < codeB, 0 if equal, 1 if codeA > codeB
  // Higher row (A < B < C < D < E), then by number (1 < 2 < ... < 5)
  const rowOrder = {A: 1, B: 2, C: 3, D: 4, E: 5};
  const [aRow, aNum] = codeA.split('') as [keyof typeof rowOrder, string];
  const [bRow, bNum] = codeB.split('') as [keyof typeof rowOrder, string];
  if (rowOrder[aRow] < rowOrder[bRow]) return -1;
  if (rowOrder[aRow] > rowOrder[bRow]) return 1;
  if (parseInt(aNum) < parseInt(bNum)) return -1;
  if (parseInt(aNum) > parseInt(bNum)) return 1;
  return 0;
};

interface InitialRiskRatingModalProps {
  show: boolean;
  onHide: () => void;
  onSelect?: (code: string) => void;
  selectedValue?: string;
  irrValue?: string; // IRR code for disabling logic
  title?: string; // For popup header
}

const ConsequenceRow = ({
  irrValue,
  selected,
  setSelected,
  onSelect,
}: {
  irrValue?: string;
  selected: string;
  setSelected: (code: string) => void;
  onSelect?: (code: string) => void;
}) => {
  const handleTileClick = (code: string) => {
    setSelected(code);
    if (onSelect) onSelect(code);
  };

  const getTileTitle = (isDisabled: boolean, isColDiff: boolean): string => {
    if (isDisabled)
      return 'Cannot select a higher risk than Initial Risk Rating';
    if (isColDiff)
      return 'You are lowering the Risk significantly. You will have to mention the reason to do so.';
    return '';
  };

  const renderTile = (
    code: string,
    rowIdx: number,
    colIdx: number,
    irrValue?: string,
    selected?: string,
  ) => {
    const isDisabled = irrValue && compareRiskCodes(code, irrValue) > 0;
    const irr = getRiskLevel(irrValue ?? selected ?? '');
    const current = getRiskLevel(code);
    const isColDiff = irr.col - current.col > 2;
    const isSelected = selected === code;

    const baseButtonStyle: React.CSSProperties = {
      gridRow: `${4 + rowIdx} / ${5 + rowIdx}`,
      gridColumn: `${4 + colIdx} / ${5 + colIdx}`,
      background: getCellColor(code),
      color: '#fff',
      fontWeight: 600,
      fontSize: 18,
      width: 200,
      height: 112,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      cursor: isDisabled ? 'not-allowed' : 'pointer',
      pointerEvents: isDisabled ? 'none' : 'auto',
      border: isSelected ? '3px solid #2361A9' : '1px solid #fff',
      boxShadow: isSelected ? '0 0 0 2px #2361A9' : undefined,
      outline: isSelected ? '2px solid #2361A9' : undefined,
      transition: 'box-shadow 0.2s, border 0.2s',
      paddingTop: 12,
      paddingRight: 8,
      paddingBottom: 12,
      paddingLeft: 8,
      margin: 0,
      appearance: 'none',
      backgroundColor: getCellColor(code),
      textAlign: 'center',
      borderRadius: 0,
      padding: 0,
      textDecoration: 'none',
    };

    return (
      <div key={code} title={getTileTitle(!!isDisabled, isColDiff)}>
        <button
          disabled={!!isDisabled}
          onClick={() => handleTileClick(code)}
          style={baseButtonStyle}
        >
          {code}
        </button>
      </div>
    );
  };

  return (
    <>
      {consequenceRows.map((row, rowIdx) => (
        <React.Fragment key={row.label}>
          {/* Consequence label */}
          <div
            style={{
              gridRow: `${4 + rowIdx} / ${5 + rowIdx}`,
              gridColumn: '2 / 3',
              background: '#F6F8FA',
              borderRight: '1px solid #e0e0e0',
              borderBottom: rowIdx < 4 ? '1px solid #e0e0e0' : undefined,
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center',
              alignItems: 'flex-start',
              padding: '12px 8px',
              writingMode: 'vertical-rl',
              transform: 'rotate(-180deg)',
              fontSize: 12,
            }}
          >
            {row.label}
          </div>
          {/* Consequence desc */}
          <div
            style={{
              gridRow: `${4 + rowIdx} / ${5 + rowIdx}`,
              gridColumn: '3 / 4',
              background: '#F6F8FA',
              borderRight: '1px solid #e0e0e0',
              borderBottom: rowIdx < 4 ? '1px solid #e0e0e0' : undefined,
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center',
              alignItems: 'flex-start',
              padding: '12px 8px',
              width: 169,
              fontSize: 12,
            }}
          >
            {row.desc}
          </div>
          {/* Tiles */}
          {row.codes.map((code, colIdx) =>
            renderTile(code, rowIdx, colIdx, irrValue, selected),
          )}
        </React.Fragment>
      ))}
    </>
  );
};

const InitialRiskRatingModal: React.FC<InitialRiskRatingModalProps> = ({
  show,
  onHide,
  onSelect,
  selectedValue,
  irrValue,
  title,
}) => {
  const [selected, setSelected] = useState(selectedValue || '');

  useEffect(() => {
    if (selectedValue !== selected) {
      setSelected(selectedValue || '');
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedValue]);

  return (
    <Modal
      show={show}
      onHide={onHide}
      centered
      style={{maxWidth: '1350px !important', width: '100%'}}
      dialogClassName="custom-risk-modal"
    >
      <Modal.Body style={{padding: 32}}>
        <div style={{fontSize: 22, fontWeight: 500, marginBottom: 16}}>
          {title}
        </div>
        <div
          style={{
            overflowX: 'auto',
            background: '#fff',
            borderRadius: 8,
            padding: 16,
          }}
        >
          <div
            style={{
              display: 'grid',
              border: '1px solid #e0e0e0',
              borderRadius: 8,
              minWidth: 1200,
              background: '#fff',
            }}
          >
            {/* Top row: two "Categories" cells */}
            <div
              style={{
                gridRow: '1 / 2',
                gridColumn: '1 / 4',
                background: '#F6F8FA',
                borderRight: '1px solid #e0e0e0',
                borderBottom: '1px solid #e0e0e0',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontWeight: 600,
                fontSize: 16,
                // height: 48,
                padding: '12px 8px',
              }}
            >
              Categories
            </div>
            <div
              style={{
                gridRow: '1 / 2',
                gridColumn: '4 / 9',
                background: '#F6F8FA',
                borderBottom: '1px solid #e0e0e0',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontWeight: 600,
                fontSize: 16,
                padding: '12px 8px',
              }}
            >
              Categories
            </div>
            <div
              style={{
                gridRow: '2 / 4',
                gridColumn: '2 / 3',
                background: '#F6F8FA',
                borderRight: '1px solid #e0e0e0',
                borderBottom: '1px solid #e0e0e0',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontWeight: 600,
                fontSize: 16,
                writingMode: 'vertical-rl',
                transform: 'rotate(-180deg)',
                letterSpacing: 1,
                padding: '12px 8px',
              }}
            >
              {' '}
            </div>
            {/* People vertical header */}
            <div
              style={{
                gridRow: '2 / 4',
                gridColumn: '3 / 3',
                background: '#F6F8FA',
                borderRight: '1px solid #e0e0e0',
                borderBottom: '1px solid #e0e0e0',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontWeight: 600,
                fontSize: 14,
                letterSpacing: 1,
                padding: '12px 8px',
              }}
            >
              People
            </div>
            {/* Consequences vertical header */}
            <div
              style={{
                gridRow: '2 / 9',
                gridColumn: '1 / 2',
                background: '#F6F8FA',
                borderRight: '1px solid #e0e0e0',
                borderBottom: '1px solid #e0e0e0',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontWeight: 600,
                fontSize: 14,
                writingMode: 'vertical-rl',
                transform: 'rotate(-180deg)',
                letterSpacing: 1,
                padding: '12px 8px',
              }}
            >
              Consequences
            </div>
            {/* Likelihood Headings */}
            {likelihoodCols.map((col, idx) => (
              <React.Fragment key={col.label}>
                <div
                  style={{
                    gridRow: '2 / 3',
                    gridColumn: `${4 + idx} / ${5 + idx}`,
                    background: '#F6F8FA',
                    borderRight: idx < 4 ? '1px solid #e0e0e0' : undefined,
                    borderBottom: '1px solid #e0e0e0',
                    padding: 8,
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    justifyContent: 'center',
                    minHeight: 80,
                    fontSize: 14,
                    width: 200,
                  }}
                >
                  {col.label}
                </div>
                <div
                  style={{
                    gridColumn: `${4 + idx} / ${5 + idx}`,
                    background: '#F6F8FA',
                    borderRight: idx < 4 ? '1px solid #e0e0e0' : undefined,
                    borderBottom: '1px solid #e0e0e0',
                    padding: 8,
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    justifyContent: 'center',
                    minHeight: 80,
                    fontSize: 12,
                    width: 200,
                  }}
                >
                  {col.desc}
                </div>
              </React.Fragment>
            ))}
            {/* Consequence Rows and Tiles */}
            <ConsequenceRow
              irrValue={irrValue}
              selected={selected}
              setSelected={setSelected}
              onSelect={onSelect}
            />
          </div>
        </div>
        <div className="d-flex justify-content-end mt-4">
          <Button variant="primary" onClick={onHide}>
            Cancel
          </Button>
        </div>
      </Modal.Body>
    </Modal>
  );
};

export default InitialRiskRatingModal;
