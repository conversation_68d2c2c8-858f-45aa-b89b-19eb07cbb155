import React, {useState, useEffect} from 'react';
import {Row, Col, Form, Alert} from 'react-bootstrap';
import {RiskCategory} from '../context/DataStoreProvider';
import SearchInput from './SearchInput';
import CheckboxComponent from './CheckboxComponent';
import {format} from 'date-fns';
import LevelOfRATag from './LevelOfRATag';
import {renderWithTooltipIfNeeded} from '../utils/renderWithTooltipIfNeeded';

type SelectableCheckboxGridProps = Readonly<{
  title: string;
  subtitle: string;
  searchPlaceholder: string;
  options: RiskCategory[];
  initialChecked?: number[];
  isOthersSelected?: boolean;
  othersText?: string;
  onOthersChange?: (flag: boolean, value: string) => void;
  onChange: (checkedIds: number[]) => void;
  hasOthers?: boolean;
  isEdit?: boolean;
  dateOfRiskAssessment?: string;
  type: 'template' | 'risk';
  feildType: 'category' | 'hazard';
  hasTemplateId?: boolean;
  isLeve1RA?: boolean;
}>;

export default function SelectableCheckboxGrid({
  title,
  subtitle,
  searchPlaceholder,
  options,
  initialChecked = [],
  isOthersSelected,
  othersText,
  onOthersChange,
  onChange,
  hasOthers = false,
  isEdit = false,
  dateOfRiskAssessment,
  type = 'template',
  feildType = 'category',
  hasTemplateId = true,
  isLeve1RA = false,
}: SelectableCheckboxGridProps) {
  const [search, setSearch] = useState<string>('');
  const [checked, setChecked] = useState<number[]>(initialChecked);

  // Call onChange whenever checked changes
  useEffect(() => {
    onChange(checked);
    // eslint-disable-next-line
  }, [checked]);

  // Checked options (excluding Others)
  const checkedOptions = options.filter(opt => checked.includes(opt?.id));
  // Unchecked options (excluding checked)
  const uncheckedOptions = options.filter(opt => !checked.includes(opt?.id));
  // Filter unchecked options by search
  const filteredUnchecked = uncheckedOptions.filter(opt =>
    opt.name.toLowerCase().includes(search.toLowerCase()),
  );

  // Split checked and unchecked into two columns
  const checkedMid = Math.ceil(checkedOptions.length / 2);
  const checkedCol1 = checkedOptions.slice(0, checkedMid);
  const checkedCol2 = checkedOptions.slice(checkedMid);

  const uncheckedMid = Math.ceil(filteredUnchecked.length / 2);
  const uncheckedCol1 = filteredUnchecked.slice(0, uncheckedMid);
  const uncheckedCol2 = filteredUnchecked.slice(uncheckedMid);

  return (
    <div>
      <div>
        {!isEdit && (
          <div style={{color: '#1F4A70', fontSize: '20px', fontWeight: 600}}>
            {renderWithTooltipIfNeeded(title)}
          </div>
        )}
        <div className="d-flex align-items-center">
          {dateOfRiskAssessment && (
            <div className="text-muted fs-14 d-flex align-items-center">
              Date of Risk Assessment:{' '}
              {format(new Date(dateOfRiskAssessment), 'dd MMM yyyy')}
            </div>
          )}
          {isLeve1RA && <LevelOfRATag />}
        </div>
        {!isEdit && <hr style={{marginRight: '-1rem', marginLeft: '-1rem'}} />}
        <Row className="align-items-center mb-3">
          <Col>
            <span style={{fontWeight: 600, fontSize: 16}}>{subtitle}</span>
          </Col>
          <Col md={3}>
            <SearchInput
              placeholder={searchPlaceholder}
              value={search}
              onSearch={e => setSearch(e || '')}
            />
          </Col>
        </Row>
        {type === 'risk' && hasTemplateId && (
          <Row>
            <Col>
              <Alert
                variant="warning"
                className="py-2 fw-400 fs-14"
                style={{
                  backgroundColor: '#FFF8E1',
                  color: '#BF7F05',
                }}
              >
                {feildType === 'category'
                  ? [
                      'The RA Categories listed might not cover all the Risks. ',
                      <b key="cat">
                        Please do add the R.A. Categories required for the Task.
                      </b>,
                    ]
                  : [
                      'The Hazard Categories listed might not cover all the Hazards. ',
                      <b key="haz">
                        Please do add the Hazards Categories required for the
                        Task.
                      </b>,
                    ]}
              </Alert>
            </Col>
          </Row>
        )}
        {/* Checked area (two columns, excluding Others) */}
        {(checkedCol1.length > 0 ||
          checkedCol2.length > 0 ||
          (hasOthers && isOthersSelected)) && (
          <Row>
            <Col md={6}>
              {checkedCol1.map(opt => (
                <CheckboxComponent
                  key={opt.id}
                  id={`checked-${opt.id}`}
                  checked={true}
                  label={opt.name}
                  onChange={() =>
                    setChecked(prev =>
                      prev.includes(opt.id)
                        ? prev.filter(o => o !== opt.id)
                        : [...prev, opt.id],
                    )
                  }
                />
              ))}
            </Col>
            <Col md={6}>
              {checkedCol2.map(opt => (
                <CheckboxComponent
                  key={opt.id}
                  id={`checked-${opt.id}`}
                  checked={true}
                  label={opt.name}
                  onChange={() =>
                    setChecked(prev =>
                      prev.includes(opt.id)
                        ? prev.filter(o => o !== opt.id)
                        : [...prev, opt.id],
                    )
                  }
                />
              ))}
            </Col>
          </Row>
        )}
        {hasOthers && isOthersSelected && (
          <Row className="mt-2">
            <Col md={8}>
              <CheckboxComponent
                id="others-checkbox"
                checked={true}
                label="Others"
                onChange={() => onOthersChange?.(false, '')}
              />
              <Form.Control
                type="text"
                placeholder="Please specify"
                value={othersText}
                onChange={e => onOthersChange?.(true, e.target.value)}
                maxLength={255}
                className="mt-2 fs-14"
                style={{width: '100%'}}
              />
            </Col>
          </Row>
        )}
        <hr className="mt-2 mb-3" />
        <Row>
          <Col md={6}>
            {uncheckedCol1.map(opt => (
              <CheckboxComponent
                key={opt.id}
                id={String(opt.id)}
                checked={false}
                label={opt.name}
                onChange={() => setChecked(prev => [...prev, opt.id])}
              />
            ))}
          </Col>
          <Col md={6}>
            {uncheckedCol2.map(opt => (
              <CheckboxComponent
                key={opt.id}
                id={String(opt.id)}
                checked={false}
                label={opt.name}
                onChange={() => setChecked(prev => [...prev, opt.id])}
              />
            ))}
          </Col>
        </Row>
        {/* "Others" as last row in unchecked area if not selected */}
        {hasOthers && !isOthersSelected && (
          <Row className="mt-2">
            <Col>
              <CheckboxComponent
                id="others-checkbox"
                checked={false}
                label="Others"
                onChange={() => onOthersChange?.(true, '')}
              />
            </Col>
          </Row>
        )}
      </div>
    </div>
  );
}
