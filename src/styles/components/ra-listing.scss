.ra-listing-page {
  .breadcrumb-header {
    font-weight: 600;
    font-size: 24px;
    line-height: 36px;
    color: #1f4a70;
  }

  .basic-btn {
    padding: 6px 12px;
    height: 36px;
    font-weight: 400;
    font-size: 16px;
  }

  .create-new-btn {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    gap: 2px;

    background: #1f4a70;
    border: 1px solid #1f4a70;
    border-radius: 4px;
    color: #ffffff;

    .dropdown-toggle::after {
      width: 20px;
      height: 20px;
    }
  }
}

.approver-tooltip,
.tooltip-inner {
  max-width: 500px !important;
  min-width: 300px !important;
  justify-self: start;
  white-space: pre-line !important;
  padding: 0.25rem;
  padding-top: 0.5rem;
  color: #fff;
  text-align: center;
  background-color: #000;
  border-radius: 0.25rem;
  display: flex;
  justify-content: flex-start;
  position: relative; // add this if not present
}

.approver-tooltip .tooltip-arrow,
.tooltip.show .tooltip-arrow {
  display: block !important;
  opacity: 1 !important;
  position: absolute;
  right: -6px; // changed from left to right
  left: auto; // ensure left is unset
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-top: 8px solid transparent;
  border-bottom: 8px solid transparent;
  border-left: 8px solid #000; // changed from border-right to border-left
  border-right: none; // remove right border
  z-index: 1;
}

.approver-tooltip {
  hr {
    border: none;
    border-top: 1px solid #444;
    margin: 10px 0;
  }

  .colored-tile {
    font-size: 12px;
    padding: 2px 8px;
    border-radius: 4px;
    display: inline-block;
    vertical-align: middle;
  }

  .approver-name-text {
    flex: 1;
    display: flex;
    justify-content: flex-start;
  }

  .approver-title {
    font-size: 12px;
    font-weight: 600;
    color: #ffff;
    margin-bottom: 2px;
    display: flex;
    justify-content: flex-start;
  }

  .approver-name {
    font-weight: 5500;
    font-size: 16px;
    color: #ffff;
    margin-bottom: 2px;
    display: flex;
    justify-content: flex-start;
  }

  .approver-meta {
    color: #6c757d;
    font-size: 14px;
    font-weight: 400;
    margin-bottom: 4px;
    display: flex;
    justify-content: flex-start;
  }

  .approver-message {
    margin-top: 4px;
    font-size: 14px;
    font-weight: 500;
    display: flex;
    color: #ffff;
    flex-direction: column;
    justify-content: flex-start;
    justify-self: flex-start;
    align-items: flex-start;
    flex-wrap: wrap;
  }
}
