import React from 'react';
import {render, screen, fireEvent, waitFor} from '@testing-library/react';
import '@testing-library/jest-dom';
import {MemoryRouter} from 'react-router-dom';
import RADraftsListing, {
  getColumns,
} from '../../../src/pages/RADrafts/RADraftsListing';
import {TemplateStatus} from '../../../src/enums';

// Mock data for drafts
const mockDraftData = [
  {
    id: 1,
    task_requiring_ra: 'Draft Risk Assessment 1',
    updated_at: '2023-01-15T10:00:00Z',
    status: TemplateStatus.DRAFT,
    created_by: 'user1',
    created_at: '2023-01-10T10:00:00Z',
  },
  {
    id: 2,
    task_requiring_ra: 'Draft Template 2',
    updated_at: '2023-02-20T14:30:00Z',
    status: TemplateStatus.DRAFT,
    created_by: 'user2',
    created_at: '2023-02-15T14:30:00Z',
  },
];

const mockUserDetails = [
  {userId: 'user1', email: '<EMAIL>'},
  {userId: 'user2', email: '<EMAIL>'},
];

// Mock dependencies
const mockNavigate = jest.fn();
const mockFetchNextPage = jest.fn();
const mockRefetch = jest.fn();

jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
}));

// Mock useDataStoreContext
jest.mock('../../../src/context', () => ({
  useDataStoreContext: jest.fn(),
}));

jest.mock('../../../src/hooks/useInfiniteQuery', () => {
  return jest.fn(() => ({
    data: {
      data: mockDraftData,
      pagination: {
        totalItems: 2,
        totalPages: 1,
        page: 1,
        pageSize: 100,
      },
      userDetails: mockUserDetails,
    },
    isFetchingNextPage: false,
    isLoading: false,
    fetchNextPage: mockFetchNextPage,
    refetch: mockRefetch,
  }));
});

jest.mock('../../../src/services/services', () => ({
  getRAList: jest.fn(),
  getRiskList: jest.fn(),
  getTemplateList: jest.fn(),
  deleteRiskById: jest.fn(),
  deleteTemplateById: jest.fn(),
}));

jest.mock('../../../src/utils/common', () => ({
  cleanObject: jest.fn(obj => obj),
  parseDate: jest.fn(date => {
    if (date === '2023-01-15T10:00:00Z') return '15 Jan 2023';
    if (date === '2023-02-20T14:30:00Z') return '20 Feb 2023';
    return '01 Jan 2023';
  }),
}));

jest.mock('../../../src/components/InfiniteScrollTable', () => {
  return function MockInfiniteScrollTable({
    columns,
    data,
    isFetchingNextPage,
    isLoading,
    fetchNextPage,
    sorting,
  }: any) {
    // Simulate the actual column cell rendering
    const renderActionCell = (item: any) => {
      const actionColumn = columns?.find((col: any) => col.id === 'action');
      if (actionColumn && actionColumn.cell) {
        const mockRow = {original: item};
        return actionColumn.cell({row: mockRow});
      }
      return null;
    };

    return (
      <div data-testid="infinite-scroll-table">
        <div data-testid="table-loading">
          {isLoading ? 'Loading...' : 'Loaded'}
        </div>
        <div data-testid="table-fetching">
          {isFetchingNextPage ? 'Fetching...' : 'Ready'}
        </div>
        <div data-testid="table-data-count">{data?.length || 0}</div>
        <div data-testid="table-columns-count">{columns?.length || 0}</div>
        <button
          data-testid="fetch-next-page"
          onClick={fetchNextPage}
          disabled={isFetchingNextPage}
        >
          Load More
        </button>
        <div data-testid="sorting-info">
          {sorting?.sorting?.[0]?.id || 'none'}-
          {sorting?.sorting?.[0]?.desc ? 'desc' : 'asc'}
        </div>
        {/* Render table data for testing */}
        {data?.map((item: any, index: number) => (
          <div key={item.id || index} data-testid={`table-row-${item.id}`}>
            <span data-testid={`task-name-${item.id}`}>
              {item.task_requiring_ra}
            </span>
            <span data-testid={`updated-at-${item.id}`}>{item.updated_at}</span>
            <div data-testid={`action-menu-${item.id}`}>
              {renderActionCell(item)}
            </div>
          </div>
        ))}
        {/* Render column headers for testing */}
        <div data-testid="column-headers">
          {columns?.map((column: any, index: number) => (
            <div
              key={index}
              data-testid={`column-header-${column.accessorKey || column.id}`}
            >
              {typeof column.header === 'function'
                ? column.header()
                : column.header}
            </div>
          ))}
        </div>
      </div>
    );
  };
});

jest.mock('../../../src/pages/RADrafts/RADraftHeader', () => ({
  RADraftHeader: function MockRADraftHeader({activeTab, setActiveTab}: any) {
    return (
      <div data-testid="ra-draft-header">
        <button
          data-testid="risk-assessment-tab"
          onClick={() => setActiveTab(1)}
          className={activeTab === 1 ? 'active' : ''}
        >
          Risk Assessment
        </button>
        <button
          data-testid="ra-template-tab"
          onClick={() => setActiveTab(2)}
          className={activeTab === 2 ? 'active' : ''}
        >
          RA Template
        </button>
      </div>
    );
  },
}));

jest.mock('../../../src/components/DiscardDraftModal', () => ({
  DiscardDraftModal: function MockDiscardDraftModal({
    onClose,
    id,
    activeTab,
  }: any) {
    return (
      <div data-testid="discard-draft-modal">
        <div data-testid="modal-draft-id">{id}</div>
        <div data-testid="modal-active-tab">{activeTab}</div>
        <button
          data-testid="modal-discard-button"
          onClick={() => onClose(true)}
        >
          Discard
        </button>
        <button
          data-testid="modal-cancel-button"
          onClick={() => onClose(false)}
        >
          Cancel
        </button>
      </div>
    );
  },
}));

jest.mock('../../../src/components/TruncateBasicText', () => {
  return function MockTruncateText({text, maxLength}: any) {
    return (
      <span data-testid="truncate-text" data-max-length={maxLength}>
        {text}
      </span>
    );
  };
});

jest.mock('../../../src/components/icons', () => ({
  ThreeDotsMenuIcon: function MockThreeDotsMenuIcon() {
    return <div data-testid="three-dots-icon">⋯</div>;
  },
}));

jest.mock('react-bootstrap', () => {
  const MockDropdown = function MockDropdown({children, className}: any) {
    return (
      <div className={className} data-testid="dropdown">
        {children}
      </div>
    );
  };

  MockDropdown.Toggle = function MockDropdownToggle({
    children,
    className,
  }: any) {
    return (
      <div className={className} data-testid="dropdown-toggle">
        {children}
      </div>
    );
  };

  MockDropdown.Menu = function MockDropdownMenu({children, className}: any) {
    return (
      <div className={className} data-testid="dropdown-menu">
        {children}
      </div>
    );
  };

  MockDropdown.Item = function MockDropdownItem({children, onClick}: any) {
    return (
      <button onClick={onClick} data-testid="dropdown-item">
        {children}
      </button>
    );
  };

  return {
    Dropdown: MockDropdown,
  };
});

const renderWithRouter = (component: React.ReactElement) => {
  return render(component, {wrapper: MemoryRouter});
};

describe('RADraftsListing Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Always ensure getRAList and getRiskList are both mocked to return a resolved promise
    const {getRAList, getRiskList} = require('../../../src/services/services');
    getRAList.mockResolvedValue({});
    getRiskList.mockResolvedValue({});

    // Mock useDataStoreContext
    const {useDataStoreContext} = require('../../../src/context');
    useDataStoreContext.mockReturnValue({
      dataStore: {
        userDetails: mockUserDetails,
      },
      roleConfig: {
        riskAssessment: {
          canEditDraftRA: true,
          canEditDraftTemplate: true,
          canDiscardDraftRA: true,
          canDiscardDraftTemplate: true,
        },
      },
    });
  });

  describe('Component Rendering', () => {
    it('renders the component correctly', () => {
      renderWithRouter(<RADraftsListing />);

      // Check header is rendered
      expect(screen.getByTestId('ra-draft-header')).toBeInTheDocument();

      // Check table is rendered
      expect(screen.getByTestId('infinite-scroll-table')).toBeInTheDocument();

      // Check data is displayed correctly
      expect(screen.getByTestId('task-name-1')).toHaveTextContent(
        'Draft Risk Assessment 1',
      );
      expect(screen.getByTestId('task-name-2')).toHaveTextContent(
        'Draft Template 2',
      );
    });

    it('renders with correct initial state', () => {
      renderWithRouter(<RADraftsListing />);

      // Check initial tab is Risk Assessment (tab 1)
      expect(screen.getByTestId('risk-assessment-tab')).toHaveClass('active');
      expect(screen.getByTestId('ra-template-tab')).not.toHaveClass('active');

      // Check table shows correct data count
      expect(screen.getByTestId('table-data-count')).toHaveTextContent('2');

      // Check table shows correct columns count (3 columns: task_requiring_ra, updated_at, action)
      expect(screen.getByTestId('table-columns-count')).toHaveTextContent('3');
    });

    it('renders table columns correctly', () => {
      renderWithRouter(<RADraftsListing />);

      // Check column headers are rendered
      expect(
        screen.getByTestId('column-header-task_requiring_ra'),
      ).toBeInTheDocument();
      expect(
        screen.getByTestId('column-header-updated_at'),
      ).toBeInTheDocument();
      expect(screen.getByTestId('column-header-action')).toBeInTheDocument();
    });

    it('displays loading state correctly', () => {
      const mockUseInfiniteQuery = require('../../../src/hooks/useInfiniteQuery');
      mockUseInfiniteQuery.mockReturnValueOnce({
        data: {
          data: [],
          pagination: {totalItems: 0, totalPages: 0, page: 0, pageSize: 0},
          userDetails: [],
        },
        isFetchingNextPage: false,
        isLoading: true,
        fetchNextPage: mockFetchNextPage,
        refetch: mockRefetch,
      });

      renderWithRouter(<RADraftsListing />);

      expect(screen.getByTestId('table-loading')).toHaveTextContent(
        'Loading...',
      );
    });

    it('displays fetching next page state correctly', () => {
      const mockUseInfiniteQuery = require('../../../src/hooks/useInfiniteQuery');
      mockUseInfiniteQuery.mockReturnValueOnce({
        data: {
          data: mockDraftData,
          pagination: {totalItems: 2, totalPages: 1, page: 1, pageSize: 100},
          userDetails: mockUserDetails,
        },
        isFetchingNextPage: true,
        isLoading: false,
        fetchNextPage: mockFetchNextPage,
        refetch: mockRefetch,
      });

      renderWithRouter(<RADraftsListing />);

      expect(screen.getByTestId('table-fetching')).toHaveTextContent(
        'Fetching...',
      );
    });
  });

  describe('Tab Switching', () => {
    it('switches to RA Template tab correctly', () => {
      renderWithRouter(<RADraftsListing />);

      // Initially on Risk Assessment tab
      expect(screen.getByTestId('risk-assessment-tab')).toHaveClass('active');

      // Click RA Template tab
      fireEvent.click(screen.getByTestId('ra-template-tab'));

      // Check tab switched
      expect(screen.getByTestId('ra-template-tab')).toHaveClass('active');
      expect(screen.getByTestId('risk-assessment-tab')).not.toHaveClass(
        'active',
      );
    });

    it('switches back to Risk Assessment tab correctly', () => {
      renderWithRouter(<RADraftsListing />);

      // Switch to RA Template tab first
      fireEvent.click(screen.getByTestId('ra-template-tab'));
      expect(screen.getByTestId('ra-template-tab')).toHaveClass('active');

      // Switch back to Risk Assessment tab
      fireEvent.click(screen.getByTestId('risk-assessment-tab'));

      // Check tab switched back
      expect(screen.getByTestId('risk-assessment-tab')).toHaveClass('active');
      expect(screen.getByTestId('ra-template-tab')).not.toHaveClass('active');
    });
  });

  describe('Draft Actions', () => {
    it('renders action dropdown for each draft', () => {
      renderWithRouter(<RADraftsListing />);

      // Check action dropdowns exist for both drafts
      const actionMenus = screen.getAllByTestId(/action-menu-/);
      expect(actionMenus).toHaveLength(2);

      // Check dropdown items exist
      const dropdownItems = screen.getAllByTestId('dropdown-item');
      expect(dropdownItems.length).toBeGreaterThanOrEqual(4); // 2 items per draft (Edit, Discard)
    });

    it('displays correct action buttons text', () => {
      renderWithRouter(<RADraftsListing />);

      // Check that Edit and Discard Draft buttons exist
      const dropdownItems = screen.getAllByTestId('dropdown-item');
      const editButtons = dropdownItems.filter(
        item => item.textContent === 'Edit',
      );
      const discardButtons = dropdownItems.filter(
        item => item.textContent === 'Discard Draft',
      );

      expect(editButtons).toHaveLength(2); // One for each draft
      expect(discardButtons).toHaveLength(2); // One for each draft
    });

    it('shows action dropdown structure correctly', () => {
      renderWithRouter(<RADraftsListing />);

      // Check dropdown structure for first draft
      const firstActionMenu = screen.getByTestId('action-menu-1');
      expect(firstActionMenu).toBeInTheDocument();

      // Check dropdown components exist
      const dropdown = firstActionMenu.querySelector(
        '[data-testid="dropdown"]',
      );
      const dropdownToggle = firstActionMenu.querySelector(
        '[data-testid="dropdown-toggle"]',
      );
      const dropdownMenu = firstActionMenu.querySelector(
        '[data-testid="dropdown-menu"]',
      );

      expect(dropdown).toBeInTheDocument();
      expect(dropdownToggle).toBeInTheDocument();
      expect(dropdownMenu).toBeInTheDocument();
    });

    it('handles edit action correctly for risk assessment tab', () => {
      renderWithRouter(<RADraftsListing />);

      // Get all Edit buttons and click the first one
      const dropdownItems = screen.getAllByTestId('dropdown-item');
      const editButtons = dropdownItems.filter(
        item => item.textContent === 'Edit',
      );

      fireEvent.click(editButtons[0]);

      // Check navigation was called with correct path for risk assessment (default tab)
      expect(mockNavigate).toHaveBeenCalledWith('/risk-assessment/risks/1');
    });

    it('handles edit action correctly for template tab', () => {
      renderWithRouter(<RADraftsListing />);

      // Switch to template tab
      fireEvent.click(screen.getByTestId('ra-template-tab'));

      // Get all Edit buttons and click the first one
      const dropdownItems = screen.getAllByTestId('dropdown-item');
      const editButtons = dropdownItems.filter(
        item => item.textContent === 'Edit',
      );

      fireEvent.click(editButtons[0]);

      // Check navigation was called with correct path for template
      expect(mockNavigate).toHaveBeenCalledWith('/risk-assessment/templates/1');
    });

    it('handles discard action correctly', () => {
      renderWithRouter(<RADraftsListing />);

      // Get all Discard Draft buttons and click the first one
      const dropdownItems = screen.getAllByTestId('dropdown-item');
      const discardButtons = dropdownItems.filter(
        item => item.textContent === 'Discard Draft',
      );

      fireEvent.click(discardButtons[0]);

      // Check modal is shown
      expect(screen.getByTestId('discard-draft-modal')).toBeInTheDocument();
      expect(screen.getByTestId('modal-draft-id')).toHaveTextContent('1');
      expect(screen.getByTestId('modal-active-tab')).toHaveTextContent('1');
    });

    it('handles discard action for second draft correctly', () => {
      renderWithRouter(<RADraftsListing />);

      // Get all Discard Draft buttons and click the second one
      const dropdownItems = screen.getAllByTestId('dropdown-item');
      const discardButtons = dropdownItems.filter(
        item => item.textContent === 'Discard Draft',
      );

      fireEvent.click(discardButtons[1]);

      // Check modal is shown with correct draft ID
      expect(screen.getByTestId('discard-draft-modal')).toBeInTheDocument();
      expect(screen.getByTestId('modal-draft-id')).toHaveTextContent('2');
    });
  });

  describe('Modal Interactions', () => {
    it('does not show modal initially', () => {
      renderWithRouter(<RADraftsListing />);

      // Modal should not be visible initially
      expect(
        screen.queryByTestId('discard-draft-modal'),
      ).not.toBeInTheDocument();
    });

    it('shows modal when discard is clicked', () => {
      renderWithRouter(<RADraftsListing />);

      // Click discard button
      const dropdownItems = screen.getAllByTestId('dropdown-item');
      const discardButtons = dropdownItems.filter(
        item => item.textContent === 'Discard Draft',
      );
      fireEvent.click(discardButtons[0]);

      // Check modal is shown
      expect(screen.getByTestId('discard-draft-modal')).toBeInTheDocument();
    });

    it('handles modal close with refetch correctly', () => {
      renderWithRouter(<RADraftsListing />);

      // Open modal by clicking discard
      const dropdownItems = screen.getAllByTestId('dropdown-item');
      const discardButtons = dropdownItems.filter(
        item => item.textContent === 'Discard Draft',
      );
      fireEvent.click(discardButtons[0]);

      expect(screen.getByTestId('discard-draft-modal')).toBeInTheDocument();

      // Click discard button in modal (which calls onClose(true))
      fireEvent.click(screen.getByTestId('modal-discard-button'));

      // Check refetch was called
      expect(mockRefetch).toHaveBeenCalled();
    });

    it('handles modal close without refetch correctly', () => {
      renderWithRouter(<RADraftsListing />);

      // Open modal by clicking discard
      const dropdownItems = screen.getAllByTestId('dropdown-item');
      const discardButtons = dropdownItems.filter(
        item => item.textContent === 'Discard Draft',
      );
      fireEvent.click(discardButtons[0]);

      expect(screen.getByTestId('discard-draft-modal')).toBeInTheDocument();

      // Reset mock to check it's not called
      mockRefetch.mockClear();

      // Click cancel button in modal (which calls onClose(false))
      fireEvent.click(screen.getByTestId('modal-cancel-button'));

      // Check refetch was not called
      expect(mockRefetch).not.toHaveBeenCalled();
    });

    it('shows dropdown menus correctly', () => {
      renderWithRouter(<RADraftsListing />);

      // Check dropdown menus exist for both drafts
      const dropdownMenus = screen.getAllByTestId('dropdown-menu');
      expect(dropdownMenus).toHaveLength(2);

      // Check dropdown items exist
      const dropdownItems = screen.getAllByTestId('dropdown-item');
      expect(dropdownItems.length).toBeGreaterThanOrEqual(4);
    });

    it('renders three dots icon correctly', () => {
      renderWithRouter(<RADraftsListing />);

      // Check three dots icons exist
      const threeDotsIcons = screen.getAllByTestId('three-dots-icon');
      expect(threeDotsIcons).toHaveLength(2); // One for each draft

      threeDotsIcons.forEach(icon => {
        expect(icon).toHaveTextContent('⋯');
      });
    });

    it('renders dropdown structure correctly', () => {
      renderWithRouter(<RADraftsListing />);

      // Check all dropdown components exist
      const dropdowns = screen.getAllByTestId('dropdown');
      const dropdownToggles = screen.getAllByTestId('dropdown-toggle');
      const dropdownMenus = screen.getAllByTestId('dropdown-menu');

      expect(dropdowns).toHaveLength(2);
      expect(dropdownToggles).toHaveLength(2);
      expect(dropdownMenus).toHaveLength(2);
    });
  });

  describe('Table Functionality', () => {
    it('calls fetchNextPage when load more is clicked', () => {
      renderWithRouter(<RADraftsListing />);

      // Click load more button
      fireEvent.click(screen.getByTestId('fetch-next-page'));

      // Check fetchNextPage was called
      expect(mockFetchNextPage).toHaveBeenCalled();
    });

    it('displays correct sorting information', () => {
      renderWithRouter(<RADraftsListing />);

      // Check sorting is disabled (empty array)
      expect(screen.getByTestId('sorting-info')).toHaveTextContent('none-asc');
    });

    it('handles empty data correctly', () => {
      const mockUseInfiniteQuery = require('../../../src/hooks/useInfiniteQuery');
      mockUseInfiniteQuery.mockReturnValueOnce({
        data: {
          data: [],
          pagination: {totalItems: 0, totalPages: 0, page: 0, pageSize: 0},
          userDetails: [],
        },
        isFetchingNextPage: false,
        isLoading: false,
        fetchNextPage: mockFetchNextPage,
        refetch: mockRefetch,
      });

      renderWithRouter(<RADraftsListing />);

      expect(screen.getByTestId('table-data-count')).toHaveTextContent('0');
    });
  });

  describe('Data Display', () => {
    it('displays draft data correctly', () => {
      renderWithRouter(<RADraftsListing />);

      // Check first draft data
      expect(screen.getByTestId('task-name-1')).toHaveTextContent(
        'Draft Risk Assessment 1',
      );
      expect(screen.getByTestId('updated-at-1')).toHaveTextContent(
        '2023-01-15T10:00:00Z',
      );

      // Check second draft data
      expect(screen.getByTestId('task-name-2')).toHaveTextContent(
        'Draft Template 2',
      );
      expect(screen.getByTestId('updated-at-2')).toHaveTextContent(
        '2023-02-20T14:30:00Z',
      );
    });

    it('displays action dropdowns for each draft', () => {
      renderWithRouter(<RADraftsListing />);

      // Check action menus exist for both drafts
      expect(screen.getByTestId('action-menu-1')).toBeInTheDocument();
      expect(screen.getByTestId('action-menu-2')).toBeInTheDocument();

      // Check dropdown items exist
      const dropdownItems = screen.getAllByTestId('dropdown-item');
      expect(dropdownItems.length).toBeGreaterThanOrEqual(4); // At least 2 items per draft
    });

    it('displays table rows correctly', () => {
      renderWithRouter(<RADraftsListing />);

      // Check table rows exist
      expect(screen.getByTestId('table-row-1')).toBeInTheDocument();
      expect(screen.getByTestId('table-row-2')).toBeInTheDocument();
    });
  });

  describe('Hook Integration', () => {
    it('calls useInfiniteQuery with correct parameters for risk tab', () => {
      const mockUseInfiniteQuery = require('../../../src/hooks/useInfiniteQuery');

      renderWithRouter(<RADraftsListing />);

      // Check useInfiniteQuery was called with correct parameters
      expect(mockUseInfiniteQuery).toHaveBeenCalledWith(
        expect.any(Function), // fetchFunction
        {
          limit: 100,
          status: TemplateStatus.DRAFT,
          sort_by: 'updated_at',
          sort_order: 'DESC',
        },
      );
    });

    it('uses correct fetch function for risk tab', () => {
      const {getRAList} = require('../../../src/services/services');

      renderWithRouter(<RADraftsListing />);

      // Get the fetch function that was passed to useInfiniteQuery
      const mockUseInfiniteQuery = require('../../../src/hooks/useInfiniteQuery');
      const fetchFunction = mockUseInfiniteQuery.mock.calls[0][0];

      // Call the fetch function and check it calls getRAList for tab 1
      const params = {page: 1, limit: 100};
      fetchFunction(params);

      expect(getRAList).toHaveBeenCalledWith(params);
    });

    it('uses correct fetch function for template tab', () => {
      renderWithRouter(<RADraftsListing />);

      // The fetch function is determined by the selectedTab state
      // Since we can't easily test the internal state change, we'll test the structure
      const mockUseInfiniteQuery = require('../../../src/hooks/useInfiniteQuery');
      const fetchFunction = mockUseInfiniteQuery.mock.calls[0][0];

      // Verify the fetch function exists and is callable
      expect(typeof fetchFunction).toBe('function');

      // The actual service call depends on the selectedTab state inside the component
      // This test verifies the function structure is correct
      const params = {page: 1, limit: 100};
      expect(() => fetchFunction(params)).not.toThrow();
    });
  });
});

describe('getColumns Function', () => {
  const mockNavigate = jest.fn();
  const mockHandleDiscardDraft = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Column Configuration', () => {
    it('returns correct number of columns', () => {
      const columns = getColumns(1, 5, mockNavigate, mockHandleDiscardDraft);
      expect(columns).toHaveLength(3);
    });

    it('configures task_requiring_ra column correctly', () => {
      const columns = getColumns(1, 5, mockNavigate, mockHandleDiscardDraft);
      const taskColumn = columns[0] as any;

      expect(taskColumn.accessorKey).toBe('task_requiring_ra');
      expect(taskColumn.header).toBe('Drafts (5)');
      expect(taskColumn.enableSorting).toBe(false);
      expect(taskColumn.minSize).toBe(220);
      expect(taskColumn.meta).toEqual({
        isSticky: true,
        stickySide: 'left',
      });
    });

    it('configures updated_at column correctly', () => {
      const columns = getColumns(1, 5, mockNavigate, mockHandleDiscardDraft);
      const updatedAtColumn = columns[1] as any;

      expect(updatedAtColumn.accessorKey).toBe('updated_at');
      expect(updatedAtColumn.header).toBe('Last Updated on');
      expect(updatedAtColumn.enableSorting).toBe(false);
      expect(updatedAtColumn.minSize).toBe(950);
    });

    it('configures action column correctly', () => {
      const columns = getColumns(1, 5, mockNavigate, mockHandleDiscardDraft);
      const actionColumn = columns[2] as any;

      expect(actionColumn.id).toBe('action');
      expect(actionColumn.header).toBe('Action');
      expect(actionColumn.enableSorting).toBe(false);
      expect(actionColumn.minSize).toBe(100);
      expect(actionColumn.meta).toEqual({
        isSticky: true,
        stickySide: 'right',
        headerAlign: 'center',
      });
    });

    it('updates header with different total counts', () => {
      const columns1 = getColumns(1, 10, mockNavigate, mockHandleDiscardDraft);
      const columns2 = getColumns(1, 25, mockNavigate, mockHandleDiscardDraft);

      expect((columns1[0] as any).header).toBe('Drafts (10)');
      expect((columns2[0] as any).header).toBe('Drafts (25)');
    });

    it('handles zero total count', () => {
      const columns = getColumns(1, 0, mockNavigate, mockHandleDiscardDraft);
      expect((columns[0] as any).header).toBe('Drafts (0)');
    });
  });

  describe('Cell Renderers', () => {
    it('renders task_requiring_ra cell correctly', () => {
      const columns = getColumns(1, 5, mockNavigate, mockHandleDiscardDraft);
      const taskColumn = columns[0] as any;

      const mockInfo = {
        getValue: () => 'Test Task Name',
      };

      const cellResult = taskColumn.cell?.(mockInfo);
      expect(cellResult).toBeDefined();
    });

    it('renders updated_at cell correctly', () => {
      const columns = getColumns(1, 5, mockNavigate, mockHandleDiscardDraft);
      const updatedAtColumn = columns[1] as any;

      const mockInfo = {
        getValue: () => '2023-01-15T10:00:00Z',
      };

      const cellResult = updatedAtColumn.cell?.(mockInfo);
      expect(cellResult).toBeDefined();
    });

    it('renders action cell correctly', () => {
      const columns = getColumns(1, 5, mockNavigate, mockHandleDiscardDraft);
      const actionColumn = columns[2] as any;

      const mockRow = {
        original: {
          id: 123,
          task_requiring_ra: 'Test Task',
        },
      };

      const cellResult = actionColumn.cell?.({row: mockRow});
      expect(cellResult).toBeDefined();
    });
  });

  describe('Action Handlers', () => {
    it('generates correct navigation path for risk tab', () => {
      const columns = getColumns(1, 5, mockNavigate, mockHandleDiscardDraft);
      const actionColumn = columns[2] as any;

      const mockRow = {
        original: {
          id: 123,
          task_requiring_ra: 'Test Task',
        },
      };

      // This would be tested by rendering the cell component and clicking edit
      // For now, we verify the column structure is correct
      expect(actionColumn.cell).toBeDefined();
    });

    it('generates correct navigation path for template tab', () => {
      const columns = getColumns(2, 5, mockNavigate, mockHandleDiscardDraft);
      const actionColumn = columns[2] as any;

      const mockRow = {
        original: {
          id: 123,
          task_requiring_ra: 'Test Task',
        },
      };

      // This would be tested by rendering the cell component and clicking edit
      // For now, we verify the column structure is correct
      expect(actionColumn.cell).toBeDefined();
    });

    it('calls handleDiscardDraft when discard is clicked', () => {
      const columns = getColumns(1, 5, mockNavigate, mockHandleDiscardDraft);
      const actionColumn = columns[2] as any;

      const mockRow = {
        original: {
          id: 123,
          task_requiring_ra: 'Test Task',
        },
      };

      // This would be tested by rendering the cell component and clicking discard
      // For now, we verify the column structure is correct
      expect(actionColumn.cell).toBeDefined();
    });
  });

  describe('Column Dependencies', () => {
    it('works with different selectedTab values', () => {
      const columns1 = getColumns(1, 5, mockNavigate, mockHandleDiscardDraft);
      const columns2 = getColumns(2, 5, mockNavigate, mockHandleDiscardDraft);

      expect(columns1).toHaveLength(3);
      expect(columns2).toHaveLength(3);

      // Both should have same structure but different behavior in action column
      expect((columns1[0] as any).accessorKey).toBe(
        (columns2[0] as any).accessorKey,
      );
      expect((columns1[1] as any).accessorKey).toBe(
        (columns2[1] as any).accessorKey,
      );
      expect((columns1[2] as any).id).toBe((columns2[2] as any).id);
    });

    it('passes all required parameters to column functions', () => {
      const columns = getColumns(1, 5, mockNavigate, mockHandleDiscardDraft);

      // Verify all columns are properly configured
      columns.forEach((column: any) => {
        expect(column).toHaveProperty('enableSorting');
        expect(column).toHaveProperty('minSize');
        if (column.cell) {
          expect(typeof column.cell).toBe('function');
        }
      });
    });
  });
});
