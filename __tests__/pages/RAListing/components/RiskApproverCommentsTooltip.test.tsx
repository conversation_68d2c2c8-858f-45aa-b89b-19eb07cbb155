import React from 'react';
import {render, screen, fireEvent} from '@testing-library/react';
import RiskApproverCommentsTooltip from '../../../../src/pages/RAListing/components/RiskApproverCommentsTooltip';

jest.mock('../../../../src/pages/CreateRA/AddApproverCard', () => ({
  getApproverStatusText: jest.fn(approver => {
    if (approver.status === 'approved') return ['Approved', 'green'];
    if (approver.status === 'rejected') return ['Rejected', 'red'];
    return ['Pending', 'grey'];
  }),
}));

jest.mock('../../../../src/components/ColoredTile', () => (props: any) => (
  <span data-testid="colored-tile">{props.text}</span>
));

jest.mock('../../../../src/utils/svgIcons', () => ({
  CommentIcon: () => <svg data-testid="comment-icon" />,
}));

const baseApprover = {
  id: 1,
  user_name: '<PERSON>',
  job_title: 'Manager',
  user_email: btoa('<EMAIL>'),
  status: 'approved',
  approval_order: 1,
  message: '',
  approval_status: 1,
};

describe('RiskApproverCommentsTooltip - additional cases', () => {
  it('renders only job title in meta if email is missing', async () => {
    const approver = {
      ...baseApprover,
      user_email: '',
    };
    render(<RiskApproverCommentsTooltip riskApprovers={[approver]} />);
    fireEvent.mouseOver(screen.getByText('1'));
    expect(
      screen.getByText(content => content.trim() === 'Manager •'),
    ).toBeInTheDocument();
  });

  it('renders only email in meta if job title is missing', async () => {
    const approver = {
      ...baseApprover,
      job_title: '',
    };
    render(<RiskApproverCommentsTooltip riskApprovers={[approver]} />);
    fireEvent.mouseOver(screen.getByText('1'));
    expect(
      screen.getByText(content => content.trim() === '• <EMAIL>'),
    ).toBeInTheDocument();
  });

  it('renders "-" for missing user_name', async () => {
    const approver = {
      ...baseApprover,
      user_name: '',
    };
    render(<RiskApproverCommentsTooltip riskApprovers={[approver]} />);
    fireEvent.mouseOver(screen.getByText('1'));
    expect(screen.getByText('-')).toBeInTheDocument();
  });

  it('renders "-" for missing status', () => {
    const approver = {
      ...baseApprover,
      status: undefined,
    };
    render(<RiskApproverCommentsTooltip riskApprovers={[approver]} />);
    // Should not count as valid approver, so dash shown
    expect(screen.getByText('-')).toBeInTheDocument();
  });

  it('renders fallback title for more than 3 approvers', async () => {
    const approvers = [
      {...baseApprover, id: 1, approval_order: 1, user_name: 'A'},
      {...baseApprover, id: 2, approval_order: 2, user_name: 'B'},
      {...baseApprover, id: 3, approval_order: 3, user_name: 'C'},
      {...baseApprover, id: 4, approval_order: 4, user_name: 'D'},
    ];
    render(<RiskApproverCommentsTooltip riskApprovers={approvers} />);
    fireEvent.mouseOver(screen.getByText('4'));
    expect(await screen.findByText('Approver 4')).toBeInTheDocument();
    expect(screen.getByText('D')).toBeInTheDocument();
  });

  it('renders divider between approvers except last', async () => {
    const approvers = [
      {...baseApprover, id: 1, approval_order: 1, user_name: 'A'},
      {...baseApprover, id: 2, approval_order: 2, user_name: 'B'},
    ];
    render(<RiskApproverCommentsTooltip riskApprovers={approvers} />);
    fireEvent.mouseOver(screen.getByText('2'));
    // There should be one <hr /> rendered
    expect(document.querySelectorAll('hr').length).toBe(1);
  });

  it('trims message before rendering', async () => {
    const approver = {
      ...baseApprover,
      message: '   Needs update   ',
    };
    render(<RiskApproverCommentsTooltip riskApprovers={[approver]} />);
    fireEvent.mouseOver(screen.getByText('1'));
    expect(screen.getByText('Needs update')).toBeInTheDocument();
  });

  it('does not render message section if message is empty', async () => {
    const approver = {
      ...baseApprover,
      message: '',
    };
    render(<RiskApproverCommentsTooltip riskApprovers={[approver]} />);
    fireEvent.mouseOver(screen.getByText('1'));
    expect(
      screen.queryByText('Condition for Approval'),
    ).not.toBeInTheDocument();
    expect(screen.queryByText('Reason for Rejection')).not.toBeInTheDocument();
  });

  it('handles null/undefined approval_order gracefully', () => {
    const approvers = [
      {...baseApprover, id: 1, approval_order: null, status: 'approved'},
      {...baseApprover, id: 2, approval_order: undefined, status: 'approved'},
    ];
    render(<RiskApproverCommentsTooltip riskApprovers={approvers} />);
    expect(screen.getByText('2')).toBeInTheDocument();
  });
});

describe('RiskApproverCommentsTooltip', () => {
  it('renders dash and icon when no approvers', () => {
    render(<RiskApproverCommentsTooltip riskApprovers={[]} />);
    expect(screen.getByTestId('comment-icon')).toBeInTheDocument();
    expect(screen.getByText('-')).toBeInTheDocument();
  });

  it('renders count and icon when approvers exist', () => {
    render(<RiskApproverCommentsTooltip riskApprovers={[baseApprover]} />);
    expect(screen.getByTestId('comment-icon')).toBeInTheDocument();
    expect(screen.getByText('1')).toBeInTheDocument();
  });

  it('shows tooltip with approver details on hover', async () => {
    render(<RiskApproverCommentsTooltip riskApprovers={[baseApprover]} />);
    const trigger = screen.getByText('1');
    fireEvent.mouseOver(trigger);

    // Tooltip content
    expect(await screen.findByText('Approver')).toBeInTheDocument();
    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByTestId('colored-tile')).toHaveTextContent('Approved');
    expect(screen.getByText('Manager • <EMAIL>')).toBeInTheDocument();
  });

  it('shows correct title for multiple approvers', async () => {
    const approvers = [
      {...baseApprover, id: 1, approval_order: 1, status: 'approved'},
      {
        ...baseApprover,
        id: 2,
        approval_order: 2,
        status: 'pending',
        user_name: 'Jane Smith',
      },
    ];
    render(<RiskApproverCommentsTooltip riskApprovers={approvers} />);
    fireEvent.mouseOver(screen.getByText('2'));

    expect(await screen.findByText('First Approver')).toBeInTheDocument();
    expect(screen.getByText('Second Approver')).toBeInTheDocument();
    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByText('Jane Smith')).toBeInTheDocument();
  });

  it('shows reason for rejection if approval_status is 2', async () => {
    const approver = {
      ...baseApprover,
      approval_status: 2,
      status: 'rejected',
      message: 'Not sufficient',
    };
    render(<RiskApproverCommentsTooltip riskApprovers={[approver]} />);
    fireEvent.mouseOver(screen.getByText('1'));

    expect(await screen.findByText('Reason for Rejection')).toBeInTheDocument();
    expect(screen.getByText('Not sufficient')).toBeInTheDocument();
  });

  it('shows condition for approval if approval_status is not 2 and message exists', async () => {
    const approver = {
      ...baseApprover,
      approval_status: 1,
      status: 'approved',
      message: 'Please update docs',
    };
    render(<RiskApproverCommentsTooltip riskApprovers={[approver]} />);
    fireEvent.mouseOver(screen.getByText('1'));

    expect(
      await screen.findByText('Condition for Approval'),
    ).toBeInTheDocument();
    expect(screen.getByText('Please update docs')).toBeInTheDocument();
  });

  it('sorts approvers by approval_order', async () => {
    const approvers = [
      {...baseApprover, id: 2, approval_order: 2, user_name: 'B'},
      {...baseApprover, id: 1, approval_order: 1, user_name: 'A'},
    ];
    render(<RiskApproverCommentsTooltip riskApprovers={approvers} />);
    fireEvent.mouseOver(screen.getByText('2'));

    // First Approver should be 'A'
    expect(await screen.findByText('A')).toBeInTheDocument();
    expect(screen.getAllByText(/Approver/)[0].textContent).toContain('First');
  });

  it('handles missing job title and email gracefully', async () => {
    const approver = {
      ...baseApprover,
      job_title: '',
      user_email: '',
    };
    render(<RiskApproverCommentsTooltip riskApprovers={[approver]} />);
    fireEvent.mouseOver(screen.getByText('1'));

    // Should not render meta if both are missing
    expect(screen.queryByText(' • ')).not.toBeInTheDocument();
  });
});
