import React from 'react';
import {render, screen, fireEvent} from '@testing-library/react';
import RAMoreFiltersDrawer from '../../../../src/pages/RAListing/components/RAMoreFiltersDrawer';

// Mocks for dependencies
jest.mock('../../../../src/components/Drawer', () => (props: any) => (
  <div data-testid="mock-drawer">
    {props.children({closeDrawer: jest.fn()})}
  </div>
));
jest.mock(
  '../../../../src/components/CustomDatePickerWithRange',
  () => (props: any) =>
    (
      <div data-testid={`mock-date-picker-${props.controlId}`}>
        {props.placeholder}
      </div>
    ),
);
jest.mock('../../../../src/components/icons', () => ({
  PlusIcon: () => <span data-testid="mock-plus-icon">+</span>,
  TrashIcon: () => <span data-testid="mock-trash-icon">🗑️</span>,
}));
jest.mock(
  '../../../../src/components/DropdownTypeahead',
  () => (props: any) => {
    const selected = props.selected ?? [];
    return (
      <select
        data-testid="mock-dropdown-typeahead"
        onChange={e => props.onChange(e.target.value)}
        value={selected[0]?.value || ''}
      >
        <option value="">Option</option>
        <option value="Approved">Approved</option>
      </select>
    );
  },
);
jest.mock('../../../../src/components/SearchDropdown', () => (props: any) => {
  const selected = props.selected ?? [];
  const value = props.value ?? [];
  return <div data-testid="mock-search-dropdown">SearchDropdown</div>;
});

const mockOptionsData = {
  vessels: [{label: 'Vessel 1', value: 1}],
  vesselCategories: [{label: 'Category 1', value: 1}],
  offices: [{label: 'Office 1', value: 1}],
};

// Ensure all filter values and selected props are arrays, not null/undefined
const defaultFilters = {
  vessel_category: [],
  vessel_type: [],
  risk_rating: [],
  status: [],
  flagged: [],
  // ...other filters as needed, all as arrays
};

describe('RAMoreFiltersDrawer', () => {
  it('renders trigger button and drawer', () => {
    render(
      <RAMoreFiltersDrawer
        onFilterChange={jest.fn()}
        optionsData={mockOptionsData}
        filters={defaultFilters}
      />,
    );
    expect(screen.getByTestId('mock-drawer')).toBeInTheDocument();
  });

  it('renders all basic filters except search', () => {
    render(
      <RAMoreFiltersDrawer
        onFilterChange={jest.fn()}
        optionsData={mockOptionsData}
        filters={defaultFilters}
      />,
    );
    expect(screen.getAllByText('Vessel Category').length).toBeGreaterThan(0);
    expect(screen.getAllByText('Level of RA').length).toBeGreaterThan(0);
    expect(screen.getAllByText('Submitted on').length).toBeGreaterThan(0);
  });

  it('shows Add More Filters dropdown and can add optional filters', () => {
    render(
      <RAMoreFiltersDrawer
        onFilterChange={jest.fn()}
        optionsData={mockOptionsData}
        filters={defaultFilters}
      />,
    );
    const addMoreDropdown = screen.getByText('Add More Filters');
    fireEvent.click(addMoreDropdown);
    const dateOfAssessment = screen.getByText('Date of Assessment');
    fireEvent.click(dateOfAssessment);
    const approvalDateRange = screen.getByText('Approval Date Range');
    fireEvent.click(approvalDateRange);
    expect(screen.getAllByText('Date of Assessment').length).toBeGreaterThan(0);
    expect(screen.getAllByText('Approval Date Range').length).toBeGreaterThan(
      0,
    );
  });

  it('renders Clear and Apply buttons', () => {
    render(
      <RAMoreFiltersDrawer
        onFilterChange={jest.fn()}
        optionsData={mockOptionsData}
        filters={defaultFilters}
      />,
    );
    expect(screen.getByText('Clear')).toBeInTheDocument();
    expect(screen.getByText('Apply')).toBeInTheDocument();
  });

  it('can remove optional filters and calls onFilterChange', () => {
    const onFilterChange = jest.fn();
    render(
      <RAMoreFiltersDrawer
        onFilterChange={onFilterChange}
        optionsData={mockOptionsData}
        filters={defaultFilters}
      />,
    );
    fireEvent.click(screen.getByText('Add More Filters'));
    fireEvent.click(screen.getByText('Date of Assessment'));
    fireEvent.click(screen.getByText('Approval Date Range'));
    const removeBtns = screen.getAllByRole('button');
    fireEvent.click(removeBtns[removeBtns.length - 1]);
    expect(onFilterChange).toHaveBeenCalled();
  });

  it('calls handleClearFilters and handleUpdateFilters', () => {
    const onFilterChange = jest.fn();
    render(
      <RAMoreFiltersDrawer
        onFilterChange={onFilterChange}
        optionsData={mockOptionsData}
        filters={defaultFilters}
      />,
    );
    fireEvent.click(screen.getByText('Clear'));
    fireEvent.click(screen.getByText('Apply'));
    expect(onFilterChange).toHaveBeenCalled();
  });

  it('toggles optional filters on and off', () => {
    render(
      <RAMoreFiltersDrawer
        onFilterChange={jest.fn()}
        optionsData={mockOptionsData}
        filters={defaultFilters}
      />,
    );
    fireEvent.click(screen.getByText('Add More Filters'));
    const dateOfAssessment = screen.getByText('Date of Assessment');
    fireEvent.click(dateOfAssessment);
    fireEvent.click(dateOfAssessment);
    expect(screen.getByText('Date of Assessment')).toBeInTheDocument();
  });
});
