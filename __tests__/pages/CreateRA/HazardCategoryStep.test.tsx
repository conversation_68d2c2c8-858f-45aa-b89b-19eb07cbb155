import React from 'react';
import {render, screen, fireEvent} from '@testing-library/react';
import '@testing-library/jest-dom';
import {HazardCategoryStep} from '../../../src/pages/CreateRA/HazardCategoryStep';
import {TemplateForm} from '../../../src/types/template';
import {RiskForm} from '../../../src/types/risk';

// Mock the dependencies
jest.mock('../../../src/context', () => ({
  useDataStoreContext: jest.fn(),
}));

jest.mock('../../../src/components/SelectableCheckboxGrid', () => {
  return function MockSelectableCheckboxGrid({
    title,
    subtitle,
    searchPlaceholder,
    options,
    initialChecked,
    isOthersSelected,
    othersText,
    onOthersChange,
    onChange,
    hasOthers,
  }: {
    title: string;
    subtitle: string;
    searchPlaceholder: string;
    options: any[];
    initialChecked?: number[];
    isOthersSelected?: boolean;
    othersText?: string;
    onOthersChange?: (flag: boolean, value: string) => void;
    onChange: (checkedIds: number[]) => void;
    hasOthers?: boolean;
  }) {
    return (
      <div data-testid="selectable-checkbox-grid">
        <div data-testid="title">{title}</div>
        <div data-testid="subtitle">{subtitle}</div>
        <div data-testid="search-placeholder">{searchPlaceholder}</div>
        <div data-testid="options-count">{options.length}</div>
        <div data-testid="initial-checked-count">
          {initialChecked?.length || 0}
        </div>
        <div data-testid="is-others-selected">
          {isOthersSelected ? 'true' : 'false'}
        </div>
        <div data-testid="others-text">{othersText || ''}</div>
        <div data-testid="has-others">{hasOthers ? 'true' : 'false'}</div>
        <button data-testid="trigger-change" onClick={() => onChange([1, 2])}>
          Trigger Change
        </button>
        <button
          data-testid="trigger-others-change"
          onClick={() => onOthersChange?.(true, 'Custom hazard')}
        >
          Trigger Others Change
        </button>
        <button
          data-testid="trigger-others-clear"
          onClick={() => onOthersChange?.(false, '')}
        >
          Clear Others
        </button>
      </div>
    );
  };
});

const mockUseDataStoreContext =
  require('../../../src/context').useDataStoreContext;

describe('HazardCategoryStep Component', () => {
  const mockSetForm = jest.fn();
  const mockOnValidate = jest.fn();

  const mockHazardsList = [
    {id: 1, name: 'Manual Handling Activities'},
    {id: 2, name: 'Working at Height'},
    {id: 3, name: 'Use of Electricity on Deck'},
    {id: 4, name: 'Hot Work (Welding, Cutting, Burning)'},
  ];

  const defaultForm: TemplateForm = {
    task_requiring_ra: 'Test Task for Hazard Assessment',
    task_duration: '2',
    task_duration_unit: 'hours',
    task_alternative_consideration: '',
    task_rejection_reason: '',
    worst_case_scenario: '',
    recovery_measures: '',
    status: 'draft',
    parameters: [],
    template_category: {
      category_id: [],
    },
    template_hazard: {
      is_other: false,
      value: '',
      hazard_id: [],
    },
    template_job: [],
    template_task_reliability_assessment: [],
  };

  beforeEach(() => {
    jest.clearAllMocks();

    mockUseDataStoreContext.mockReturnValue({
      dataStore: {
        hazardsList: mockHazardsList,
      },
    });
  });

  it('renders correctly with default props', () => {
    render(
      <HazardCategoryStep
        form={defaultForm}
        setForm={mockSetForm}
        onValidate={mockOnValidate}
      />,
    );

    expect(screen.getByTestId('selectable-checkbox-grid')).toBeInTheDocument();
    expect(screen.getByTestId('title')).toHaveTextContent(
      'Test Task for Hazard Assessment',
    );
    expect(screen.getByTestId('subtitle')).toHaveTextContent(
      'Select all the possible Hazard Category',
    );
    expect(screen.getByTestId('search-placeholder')).toHaveTextContent(
      'Search Hazard Category',
    );
    expect(screen.getByTestId('has-others')).toHaveTextContent('true');
  });

  it('passes correct hazards list from data store', () => {
    render(
      <HazardCategoryStep
        form={defaultForm}
        setForm={mockSetForm}
        onValidate={mockOnValidate}
      />,
    );

    expect(screen.getByTestId('options-count')).toHaveTextContent('4');
  });

  it('handles empty hazards list gracefully', () => {
    mockUseDataStoreContext.mockReturnValue({
      dataStore: {
        hazardsList: null,
      },
    });

    render(
      <HazardCategoryStep
        form={defaultForm}
        setForm={mockSetForm}
        onValidate={mockOnValidate}
      />,
    );

    expect(screen.getByTestId('options-count')).toHaveTextContent('0');
  });

  it('passes initial checked hazards correctly', () => {
    const formWithSelectedHazards = {
      ...defaultForm,
      template_hazard: {
        is_other: false,
        value: '',
        hazard_id: [1, 3],
      },
    };

    render(
      <HazardCategoryStep
        form={formWithSelectedHazards}
        setForm={mockSetForm}
        onValidate={mockOnValidate}
      />,
    );

    expect(screen.getByTestId('initial-checked-count')).toHaveTextContent('2');
  });

  it('passes others selection state correctly', () => {
    const formWithOthersSelected = {
      ...defaultForm,
      template_hazard: {
        is_other: true,
        value: 'Custom hazard category',
        hazard_id: [],
      },
    };

    render(
      <HazardCategoryStep
        form={formWithOthersSelected}
        setForm={mockSetForm}
        onValidate={mockOnValidate}
      />,
    );

    expect(screen.getByTestId('is-others-selected')).toHaveTextContent('true');
    expect(screen.getByTestId('others-text')).toHaveTextContent(
      'Custom hazard category',
    );
  });

  it('handles hazard selection changes correctly', () => {
    render(
      <HazardCategoryStep
        form={defaultForm}
        setForm={mockSetForm}
        onValidate={mockOnValidate}
      />,
    );

    const triggerChangeButton = screen.getByTestId('trigger-change');
    fireEvent.click(triggerChangeButton);

    expect(mockSetForm).toHaveBeenCalledWith(expect.any(Function));

    // Test the function passed to setForm
    const setFormCall = mockSetForm.mock.calls[0][0];
    const result = setFormCall(defaultForm);

    expect(result).toEqual({
      ...defaultForm,
      template_hazard: {
        ...defaultForm.template_hazard,
        hazard_id: [1, 2],
      },
    });
  });

  it('handles others selection changes correctly', () => {
    render(
      <HazardCategoryStep
        form={defaultForm}
        setForm={mockSetForm}
        onValidate={mockOnValidate}
      />,
    );

    const triggerOthersButton = screen.getByTestId('trigger-others-change');
    fireEvent.click(triggerOthersButton);

    expect(mockSetForm).toHaveBeenCalledWith(expect.any(Function));

    // Test the function passed to setForm
    const setFormCall = mockSetForm.mock.calls[0][0];
    const result = setFormCall(defaultForm);

    expect(result).toEqual({
      ...defaultForm,
      template_hazard: {
        ...defaultForm.template_hazard,
        is_other: true,
        value: 'Custom hazard',
      },
    });
  });

  it('clears others text when others is deselected', () => {
    const formWithOthers = {
      ...defaultForm,
      template_hazard: {
        is_other: true,
        value: 'Some custom text',
        hazard_id: [],
      },
    };

    render(
      <HazardCategoryStep
        form={formWithOthers}
        setForm={mockSetForm}
        onValidate={mockOnValidate}
      />,
    );

    const clearOthersButton = screen.getByTestId('trigger-others-clear');
    fireEvent.click(clearOthersButton);

    expect(mockSetForm).toHaveBeenCalledWith(expect.any(Function));

    // Test the function passed to setForm
    const setFormCall = mockSetForm.mock.calls[0][0];
    const result = setFormCall(formWithOthers);

    expect(result).toEqual({
      ...formWithOthers,
      template_hazard: {
        ...formWithOthers.template_hazard,
        is_other: false,
        value: '',
      },
    });
  });

  it('validates correctly when no hazards are selected', () => {
    const ref = React.createRef<{validate: () => boolean}>();

    render(
      <HazardCategoryStep
        ref={ref}
        form={defaultForm}
        setForm={mockSetForm}
        onValidate={mockOnValidate}
      />,
    );

    const isValid = ref.current?.validate();
    expect(isValid).toBe(false);
    expect(mockOnValidate).toHaveBeenCalledWith(false);
  });

  it('validates correctly when hazards are selected', () => {
    const formWithSelectedHazards = {
      ...defaultForm,
      template_hazard: {
        is_other: false,
        value: '',
        hazard_id: [1, 2],
      },
    };

    const ref = React.createRef<{validate: () => boolean}>();

    render(
      <HazardCategoryStep
        ref={ref}
        form={formWithSelectedHazards}
        setForm={mockSetForm}
        onValidate={mockOnValidate}
      />,
    );

    const isValid = ref.current?.validate();
    expect(isValid).toBe(true);
    expect(mockOnValidate).toHaveBeenCalledWith(true);
  });

  it('validates correctly when others is selected with text', () => {
    const formWithOthersText = {
      ...defaultForm,
      template_hazard: {
        is_other: true,
        value: 'Custom hazard category',
        hazard_id: [],
      },
    };

    const ref = React.createRef<{validate: () => boolean}>();

    render(
      <HazardCategoryStep
        ref={ref}
        form={formWithOthersText}
        setForm={mockSetForm}
        onValidate={mockOnValidate}
      />,
    );

    const isValid = ref.current?.validate();
    expect(isValid).toBe(true);
    expect(mockOnValidate).toHaveBeenCalledWith(true);
  });

  it('validates correctly when others is selected but text is empty', () => {
    const formWithEmptyOthersText = {
      ...defaultForm,
      template_hazard: {
        is_other: true,
        value: '',
        hazard_id: [],
      },
    };

    const ref = React.createRef<{validate: () => boolean}>();

    render(
      <HazardCategoryStep
        ref={ref}
        form={formWithEmptyOthersText}
        setForm={mockSetForm}
        onValidate={mockOnValidate}
      />,
    );

    const isValid = ref.current?.validate();
    expect(isValid).toBe(false);
    expect(mockOnValidate).toHaveBeenCalledWith(false);
  });

  it('validates correctly when others has only whitespace', () => {
    const formWithWhitespaceOthersText = {
      ...defaultForm,
      template_hazard: {
        is_other: true,
        value: '   ',
        hazard_id: [],
      },
    };

    const ref = React.createRef<{validate: () => boolean}>();

    render(
      <HazardCategoryStep
        ref={ref}
        form={formWithWhitespaceOthersText}
        setForm={mockSetForm}
        onValidate={mockOnValidate}
      />,
    );

    const isValid = ref.current?.validate();
    expect(isValid).toBe(false);
    expect(mockOnValidate).toHaveBeenCalledWith(false);
  });

  it('validates correctly when both hazards and others are selected', () => {
    const formWithBothSelected = {
      ...defaultForm,
      template_hazard: {
        is_other: true,
        value: 'Custom hazard',
        hazard_id: [1, 2],
      },
    };

    const ref = React.createRef<{validate: () => boolean}>();

    render(
      <HazardCategoryStep
        ref={ref}
        form={formWithBothSelected}
        setForm={mockSetForm}
        onValidate={mockOnValidate}
      />,
    );

    const isValid = ref.current?.validate();
    expect(isValid).toBe(true);
    expect(mockOnValidate).toHaveBeenCalledWith(true);
  });

  it('calls onValidate on form changes', () => {
    const {rerender} = render(
      <HazardCategoryStep
        form={defaultForm}
        setForm={mockSetForm}
        onValidate={mockOnValidate}
      />,
    );

    // Clear previous calls
    mockOnValidate.mockClear();

    const updatedForm = {
      ...defaultForm,
      template_hazard: {
        is_other: false,
        value: '',
        hazard_id: [1],
      },
    };

    rerender(
      <HazardCategoryStep
        form={updatedForm}
        setForm={mockSetForm}
        onValidate={mockOnValidate}
      />,
    );

    expect(mockOnValidate).toHaveBeenCalledWith(true);
  });

  it('works without onValidate callback', () => {
    const ref = React.createRef<{validate: () => boolean}>();

    expect(() => {
      render(
        <HazardCategoryStep
          ref={ref}
          form={defaultForm}
          setForm={mockSetForm}
        />,
      );
    }).not.toThrow();

    const isValid = ref.current?.validate();
    expect(isValid).toBe(false);
  });

  it('handles undefined template_hazard gracefully', () => {
    const formWithUndefinedHazard = {
      ...defaultForm,
      template_hazard: undefined as any,
    };

    const ref = React.createRef<{validate: () => boolean}>();

    render(
      <HazardCategoryStep
        ref={ref}
        form={formWithUndefinedHazard}
        setForm={mockSetForm}
        onValidate={mockOnValidate}
      />,
    );

    expect(screen.getByTestId('initial-checked-count')).toHaveTextContent('0');
    expect(screen.getByTestId('is-others-selected')).toHaveTextContent('false');
    expect(screen.getByTestId('others-text')).toHaveTextContent('');

    const isValid = ref.current?.validate();
    // When template_hazard is undefined, validation returns false because:
    // selected.length (0) > 0 || isOthers (undefined) = false || undefined = false
    expect(isValid).toBe(false);
  });

  it('handles missing task_requiring_ra gracefully', () => {
    const formWithoutTask = {
      ...defaultForm,
      task_requiring_ra: '',
    };

    render(
      <HazardCategoryStep
        form={formWithoutTask}
        setForm={mockSetForm}
        onValidate={mockOnValidate}
      />,
    );

    expect(screen.getByTestId('title')).toHaveTextContent('');
  });

  it('exposes validate method through ref', () => {
    const ref = React.createRef<{validate: () => boolean}>();

    render(
      <HazardCategoryStep
        ref={ref}
        form={defaultForm}
        setForm={mockSetForm}
        onValidate={mockOnValidate}
      />,
    );

    expect(ref.current).toBeDefined();
    expect(typeof ref.current?.validate).toBe('function');
  });

  it('validates on component mount', () => {
    render(
      <HazardCategoryStep
        form={defaultForm}
        setForm={mockSetForm}
        onValidate={mockOnValidate}
      />,
    );

    expect(mockOnValidate).toHaveBeenCalledWith(false);
  });

  describe('Risk Form Type', () => {
    const defaultRiskForm: RiskForm = {
      risk_hazard: {
        hazard_id: [],
        is_other: false,
        value: '',
      },
      date_risk_assessment: '2023-01-01',
      risk_job: [],
      risk_task_reliability_assessment: [],
    };

    it('renders correctly with risk form type', () => {
      render(
        <HazardCategoryStep
          form={defaultRiskForm}
          setForm={mockSetForm}
          onValidate={mockOnValidate}
          type="risk"
        />,
      );

      expect(
        screen.getByTestId('selectable-checkbox-grid'),
      ).toBeInTheDocument();
    });

    it('handles risk form hazard selection changes correctly', () => {
      render(
        <HazardCategoryStep
          form={defaultRiskForm}
          setForm={mockSetForm}
          onValidate={mockOnValidate}
          type="risk"
        />,
      );

      // Use the mock component's trigger button instead of actual checkboxes
      const triggerButton = screen.getByTestId('trigger-change');
      fireEvent.click(triggerButton);

      expect(mockSetForm).toHaveBeenCalledWith(expect.any(Function));

      // Test the function passed to setForm
      const setFormCall = mockSetForm.mock.calls[0][0];
      const updatedForm = setFormCall(defaultRiskForm);

      expect(updatedForm.risk_hazard.hazard_id).toContain(1);
    });

    it('handles risk form others selection changes correctly', () => {
      render(
        <HazardCategoryStep
          form={defaultRiskForm}
          setForm={mockSetForm}
          onValidate={mockOnValidate}
          type="risk"
        />,
      );

      // Use the mock component's trigger button for others
      const triggerOthersButton = screen.getByTestId('trigger-others-change');
      fireEvent.click(triggerOthersButton);

      expect(mockSetForm).toHaveBeenCalledWith(expect.any(Function));

      // Test the function passed to setForm
      const setFormCall = mockSetForm.mock.calls[0][0];
      const updatedForm = setFormCall(defaultRiskForm);

      expect(updatedForm.risk_hazard.is_other).toBe(true);
    });

    it('handles risk form with pre-selected hazards', () => {
      const riskFormWithSelectedHazards: RiskForm = {
        ...defaultRiskForm,
        risk_hazard: {
          hazard_id: [1, 2],
          is_other: false,
          value: '',
        },
      };

      render(
        <HazardCategoryStep
          form={riskFormWithSelectedHazards}
          setForm={mockSetForm}
          onValidate={mockOnValidate}
          type="risk"
        />,
      );

      // Check that the initial checked count reflects the pre-selected hazards
      const initialCheckedCount = screen.getByTestId('initial-checked-count');
      expect(initialCheckedCount).toHaveTextContent('2');
    });

    it('handles risk form with others selected and text', () => {
      const riskFormWithOthers: RiskForm = {
        ...defaultRiskForm,
        risk_hazard: {
          hazard_id: [],
          is_other: true,
          value: 'Custom hazard description',
        },
      };

      render(
        <HazardCategoryStep
          form={riskFormWithOthers}
          setForm={mockSetForm}
          onValidate={mockOnValidate}
          type="risk"
        />,
      );

      // Check that others is selected and has the correct text
      const isOthersSelected = screen.getByTestId('is-others-selected');
      const othersText = screen.getByTestId('others-text');

      expect(isOthersSelected).toHaveTextContent('true');
      expect(othersText).toHaveTextContent('Custom hazard description');
    });

    it('validates risk form correctly when hazards are selected', () => {
      const ref = React.createRef<{validate: () => boolean}>();
      const riskFormWithSelectedHazards: RiskForm = {
        ...defaultRiskForm,
        risk_hazard: {
          hazard_id: [1],
          is_other: false,
          value: '',
        },
      };

      render(
        <HazardCategoryStep
          ref={ref}
          form={riskFormWithSelectedHazards}
          setForm={mockSetForm}
          onValidate={mockOnValidate}
          type="risk"
        />,
      );

      const isValid = ref.current?.validate();
      expect(isValid).toBe(true);
    });

    it('validates risk form correctly when others is selected with text', () => {
      const ref = React.createRef<{validate: () => boolean}>();
      const riskFormWithOthersText: RiskForm = {
        ...defaultRiskForm,
        risk_hazard: {
          hazard_id: [],
          is_other: true,
          value: 'Custom hazard',
        },
      };

      render(
        <HazardCategoryStep
          ref={ref}
          form={riskFormWithOthersText}
          setForm={mockSetForm}
          onValidate={mockOnValidate}
          type="risk"
        />,
      );

      const isValid = ref.current?.validate();
      expect(isValid).toBe(true);
    });
  });
});
