import {getInitials} from '../../src/utils/user';

describe('getInitials', () => {
  it('returns empty string for undefined', () => {
    expect(getInitials()).toBe('');
  });

  it('returns empty string for empty string', () => {
    expect(getInitials('')).toBe('');
  });

  it('returns first two letters uppercased for single word', () => {
    expect(getInitials('alice')).toBe('AL');
    expect(getInitials('b')).toBe('B');
    expect(getInitials('xy')).toBe('XY');
  });

  it('returns initials for two words', () => {
    expect(getInitials('Alice Bob')).toBe('AB');
    expect(getInitials('john doe')).toBe('JD');
  });

  it('returns initials for more than two words', () => {
    expect(getInitials('<PERSON>')).toBe('AB');
    expect(getInitials('<PERSON> Public')).toBe('JQ');
  });

  it('handles extra spaces', () => {
    expect(getInitials('  Alice   Bob  ')).toBe('AB');
    expect(getInitials('   ')).toBe('');
  });

  it('handles names with non-letters', () => {
    expect(getInitials('A1 B2')).toBe('AB');
    expect(getInitials('A!@ #$%')).toBe('A#');
  });
});
