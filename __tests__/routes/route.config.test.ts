import routesConfig, {<PERSON><PERSON>out<PERSON>} from '../../src/routes/route.config';
import {StepperPage} from '../../src/pages/CreateRA/CreateRA.page';
import {UserRoleControllerConfig} from '../../src/types';

describe('route.config', () => {
  // Mock role configurations
  const createMockRoleConfig = (
    hasPermission: boolean,
  ): UserRoleControllerConfig => ({
    user: {
      sub: 'test-user-id',
      email: '<EMAIL>',
      name: 'Test User',
      preferred_username: 'testuser',
      given_name: 'Test',
      family_name: 'User',
      user_id: 'testuser',
      email_verified: true,
      is_nova_onboarded: true,
      is_user_onboarded: true,
      group: ['test-group'],
      exp: **********,
      iat: **********,
      auth_time: **********,
      jti: 'test-jti',
      iss: 'test-issuer',
      aud: 'test-audience',
      typ: 'Bearer',
      azp: 'test-azp',
      nonce: 'test-nonce',
      session_state: 'test-session',
      acr: '1',
      'allowed-origins': ['http://localhost'],
      realm_access: {roles: []},
      resource_access: {account: {roles: []}},
      scope: 'openid profile email',
      user_name_hash: 'test-hash',
      tc_nova_version: 1,
    },
    riskAssessment: {
      hasPermision: hasPermission,
      canCreateNewTemplate: hasPermission,
      canViewTemplate: hasPermission,
      canViewRA: hasPermission,
      canViewDraftRA: hasPermission,
      canViewDraftTemplate: hasPermission,
      canAddTemplate: hasPermission,
      canEditDraftTemplate: hasPermission,
      canAddRA: hasPermission,
      canEditDraftRA: hasPermission,
    },
  });

  describe('IRoute interface', () => {
    it('should define correct interface structure', () => {
      const mockRoute: IRoute = {
        path: '/test-path',
        component: () => null,
        redirect: '/redirect-path',
        childRoutes: [],
        isPermission: true,
      };

      expect(mockRoute).toHaveProperty('path');
      expect(mockRoute).toHaveProperty('component');
      expect(mockRoute).toHaveProperty('redirect');
      expect(mockRoute).toHaveProperty('childRoutes');
      expect(mockRoute).toHaveProperty('isPermission');
    });

    it('should allow optional properties', () => {
      const minimalRoute: IRoute = {
        path: '/minimal-path',
      };

      expect(minimalRoute.path).toBe('/minimal-path');
      expect(minimalRoute.component).toBeUndefined();
      expect(minimalRoute.redirect).toBeUndefined();
      expect(minimalRoute.childRoutes).toBeUndefined();
      expect(minimalRoute.isPermission).toBeUndefined();
    });
  });

  describe('routesConfig function', () => {
    describe('Basic functionality', () => {
      it('should return an array of routes', () => {
        const roleConfig = createMockRoleConfig(true);
        const routes = routesConfig(roleConfig);

        expect(Array.isArray(routes)).toBe(true);
        expect(routes.length).toBe(11);
      });

      it('should return routes with correct structure', () => {
        const roleConfig = createMockRoleConfig(true);
        const routes = routesConfig(roleConfig);

        routes.forEach(route => {
          expect(route).toHaveProperty('path');
          expect(route).toHaveProperty('component');
          expect(route).toHaveProperty('isPermission');
          expect(typeof route.path).toBe('string');
          expect(typeof route.component).toBe('function');
          expect(typeof route.isPermission).toBe('boolean');
        });
      });
    });

    describe('Route definitions', () => {
      it('should define template creation route correctly', () => {
        const roleConfig = createMockRoleConfig(true);
        const routes = routesConfig(roleConfig);
        const templateCreationRoute = routes.find(
          route => route.path === 'risk-assessment/templates/create',
        );

        expect(templateCreationRoute).toBeDefined();
        expect(templateCreationRoute?.component).toBe(StepperPage);
        expect(templateCreationRoute?.isPermission).toBe(true);
      });

      it('should define template selection route with correct permission', () => {
        const roleConfig = createMockRoleConfig(true);
        const routes = routesConfig(roleConfig);
        const templateSelectionRoute = routes.find(
          route => route.path === 'risk-assessment/template-selection',
        );

        expect(templateSelectionRoute).toBeDefined();
        expect(templateSelectionRoute?.isPermission).toBe(
          roleConfig.riskAssessment.canCreateNewTemplate,
        );
      });

      it('should define all StepperPage routes correctly', () => {
        const roleConfig = createMockRoleConfig(true);
        const routes = routesConfig(roleConfig);
        const stepperRoutes = routes.filter(
          route => route.component === StepperPage,
        );

        expect(stepperRoutes).toHaveLength(5);
        const expectedStepperPaths = [
          'risk-assessment/templates/create',
          'risk-assessment/templates/:id',
          'risk-assessment/risks/create',
          'risk-assessment/risks/:id',
          'risk-assessment/templates/:id/risks/create',
        ];

        stepperRoutes.forEach(route => {
          expect(expectedStepperPaths).toContain(route.path);
        });
      });
    });

    describe('Permission handling', () => {
      it('should set isPermission to true when user has permission', () => {
        const roleConfig = createMockRoleConfig(true);
        const routes = routesConfig(roleConfig);

        routes.forEach(route => {
          expect(route.isPermission).toBe(true);
        });
      });

      it('should set isPermission to false when user lacks permission', () => {
        const roleConfig = createMockRoleConfig(false);
        const routes = routesConfig(roleConfig);

        routes.forEach(route => {
          expect(route.isPermission).toBe(false);
        });
      });

      it('should use roleConfig.riskAssessment.hasPermision for most routes', () => {
        const roleConfigWithPermission = createMockRoleConfig(true);
        const roleConfigWithoutPermission = createMockRoleConfig(false);

        const routesWithPermission = routesConfig(roleConfigWithPermission);
        const routesWithoutPermission = routesConfig(
          roleConfigWithoutPermission,
        );

        // Most routes should use hasPermision, except template-selection which uses canCreateNewTemplate
        routesWithPermission.forEach(route => {
          if (route.path === 'risk-assessment/template-selection') {
            expect(route.isPermission).toBe(
              roleConfigWithPermission.riskAssessment.canCreateNewTemplate,
            );
          } else {
            expect(route.isPermission).toBe(
              roleConfigWithPermission.riskAssessment.hasPermision,
            );
          }
        });

        routesWithoutPermission.forEach(route => {
          if (route.path === 'risk-assessment/template-selection') {
            expect(route.isPermission).toBe(
              roleConfigWithoutPermission.riskAssessment.canCreateNewTemplate,
            );
          } else {
            expect(route.isPermission).toBe(
              roleConfigWithoutPermission.riskAssessment.hasPermision,
            );
          }
        });
      });
    });

    describe('Route paths', () => {
      it('should have correct path structure', () => {
        const roleConfig = createMockRoleConfig(true);
        const routes = routesConfig(roleConfig);
        const expectedPaths = [
          'risk-assessment',
          'risk-assessment/template-listing',
          'risk-assessment/drafts',
          'risk-assessment/templates/create',
          'risk-assessment/templates/:id',
          'risk-assessment/risks/create',
          'risk-assessment/risks/:id',
          'risk-assessment/templates/:id/risks/create',
          'risk-assessment/template-selection',
          'risk-assessment/approval/:id',
          'risk-assessment/template-listing/view/:id',
        ];

        const actualPaths = routes.map(route => route.path);
        expect(actualPaths).toEqual(expectedPaths);
      });

      it('should not have leading slashes in paths', () => {
        const roleConfig = createMockRoleConfig(true);
        const routes = routesConfig(roleConfig);

        routes.forEach(route => {
          expect(route.path.startsWith('/')).toBe(false);
        });
      });

      it('should have hierarchical path structure', () => {
        const roleConfig = createMockRoleConfig(true);
        const routes = routesConfig(roleConfig);

        // All paths should start with 'risk-assessment'
        routes.forEach(route => {
          expect(route.path.startsWith('risk-assessment')).toBe(true);
        });
      });
    });

    describe('Component assignments', () => {
      it('should have all components defined', () => {
        const roleConfig = createMockRoleConfig(true);
        const routes = routesConfig(roleConfig);

        routes.forEach(route => {
          expect(route.component).toBeDefined();
          expect(typeof route.component).toBe('function');
        });
      });
    });

    describe('Edge cases and error handling', () => {
      it('should handle null roleConfig gracefully', () => {
        const nullRoleConfig = null as any;

        expect(() => routesConfig(nullRoleConfig)).toThrow();
      });

      it('should handle undefined roleConfig gracefully', () => {
        const undefinedRoleConfig = undefined as any;

        expect(() => routesConfig(undefinedRoleConfig)).toThrow();
      });

      it('should handle roleConfig without riskAssessment property', () => {
        const incompleteRoleConfig = {
          user: createMockRoleConfig(true).user,
        } as any;

        expect(() => routesConfig(incompleteRoleConfig)).toThrow();
      });

      it('should handle roleConfig with null riskAssessment', () => {
        const roleConfigWithNullRiskAssessment = {
          ...createMockRoleConfig(true),
          riskAssessment: null,
        } as any;

        expect(() => routesConfig(roleConfigWithNullRiskAssessment)).toThrow();
      });
    });

    describe('Return value consistency', () => {
      it('should return consistent results for same input', () => {
        const roleConfig = createMockRoleConfig(true);
        const routes1 = routesConfig(roleConfig);
        const routes2 = routesConfig(roleConfig);

        expect(routes1).toEqual(routes2);
      });

      it('should return different results for different permissions', () => {
        const roleConfigWithPermission = createMockRoleConfig(true);
        const roleConfigWithoutPermission = createMockRoleConfig(false);

        const routesWithPermission = routesConfig(roleConfigWithPermission);
        const routesWithoutPermission = routesConfig(
          roleConfigWithoutPermission,
        );

        expect(routesWithPermission).not.toEqual(routesWithoutPermission);

        // Paths and components should be same, only permissions different
        routesWithPermission.forEach((route, index) => {
          expect(route.path).toBe(routesWithoutPermission[index].path);
          expect(route.component).toBe(
            routesWithoutPermission[index].component,
          );
          expect(route.isPermission).not.toBe(
            routesWithoutPermission[index].isPermission,
          );
        });
      });
    });

    describe('Route configuration completeness', () => {
      it('should not have redirect properties', () => {
        const roleConfig = createMockRoleConfig(true);
        const routes = routesConfig(roleConfig);

        routes.forEach(route => {
          expect(route.redirect).toBeUndefined();
        });
      });

      it('should not have childRoutes properties', () => {
        const roleConfig = createMockRoleConfig(true);
        const routes = routesConfig(roleConfig);

        routes.forEach(route => {
          expect(route.childRoutes).toBeUndefined();
        });
      });

      it('should have all required properties for routing', () => {
        const roleConfig = createMockRoleConfig(true);
        const routes = routesConfig(roleConfig);

        routes.forEach(route => {
          expect(route.path).toBeDefined();
          expect(route.component).toBeDefined();
          expect(route.isPermission).toBeDefined();
        });
      });
    });
  });

  describe('Integration with routing system', () => {
    it('should be compatible with React Router structure', () => {
      const roleConfig = createMockRoleConfig(true);
      const routes = routesConfig(roleConfig);

      // Routes should have structure compatible with React Router
      routes.forEach(route => {
        expect(typeof route.path).toBe('string');
        expect(route.path.length).toBeGreaterThan(0);
        expect(typeof route.component).toBe('function');
      });
    });

    it('should provide all necessary route information', () => {
      const roleConfig = createMockRoleConfig(true);
      const routes = routesConfig(roleConfig);

      // Each route should have minimum required information
      routes.forEach(route => {
        expect(route).toHaveProperty('path');
        expect(route).toHaveProperty('component');
        expect(route).toHaveProperty('isPermission');
      });
    });
  });
});
