import '@testing-library/jest-dom';
import React from 'react';

// Mock react-router-dom
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: jest.fn(),
  useLocation: jest.fn(() => ({
    pathname: '/',
    search: '',
    hash: '',
    state: null,
  })),
  useParams: jest.fn(() => ({id: 'test-id'})),
  BrowserRouter: ({children}: any) => children,
  MemoryRouter: ({children}: any) => children,
  Router: ({children}: any) => children,
  Routes: ({children}: any) => children,
  Route: ({children}: any) => children,
  Link: ({children, to, ...props}: any) => {
    const mockReact = require('react');
    return mockReact.createElement('a', {href: to, ...props}, children);
  },
  NavLink: ({children, to, ...props}: any) => {
    const mockReact = require('react');
    return mockReact.createElement('a', {href: to, ...props}, children);
  },
}));

// Mock react-toastify
jest.mock('react-toastify', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
    info: jest.fn(),
    warning: jest.fn(),
  },
  ToastContainer: () => null,
}));

// Suppress React 18 warnings in tests
const originalError = console.error;
beforeAll(() => {
  console.error = (...args: any[]) => {
    if (
      typeof args[0] === 'string' &&
      (args[0].includes('ReactDOM.render is no longer supported') ||
        args[0].includes('ReactDOMTestUtils.act is deprecated') ||
        args[0].includes('unmountComponentAtNode is deprecated'))
    ) {
      return;
    }
    originalError.call(console, ...args);
  };
});

afterAll(() => {
  console.error = originalError;
});

// Global test utilities
global.ResizeObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // deprecated
    removeListener: jest.fn(), // deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

// Mock IntersectionObserver
global.IntersectionObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

// Mock scrollTo for DOM elements
Element.prototype.scrollTo = jest.fn();
Element.prototype.scroll = jest.fn();
Element.prototype.scrollIntoView = jest.fn();

// Mock HTMLElement methods
HTMLElement.prototype.scrollTo = jest.fn();
HTMLElement.prototype.scroll = jest.fn();
HTMLElement.prototype.scrollIntoView = jest.fn();
