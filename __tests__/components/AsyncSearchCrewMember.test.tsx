import React from 'react';
import {
  render,
  fireEvent,
  screen,
  act,
  cleanup,
  waitFor,
} from '@testing-library/react';
import {toast} from 'react-toastify';
import {AsyncSearchCrewMember} from '../../src/components/SearchCrewMember';

jest.mock('react-toastify', () => ({toast: {error: jest.fn()}}));

const mockOptions = [
  {id: '1', full_name: '<PERSON>', subText: 'Pilot'},
  {id: '2', full_name: '<PERSON>', subText: 'Co-pilot'},
];

const mockOriginalData = [
  {id: '1', extra: 'foo'},
  {id: '2', extra: 'bar'},
];

describe('AsyncSearchCrewMember', () => {
  afterEach(() => {
    cleanup();
    jest.clearAllMocks();
  });

  it('renders with default placeholder and fetches data', async () => {
    const fetchQuery = jest.fn().mockResolvedValue({
      options: mockOptions,
      originalData: mockOriginalData,
    });
    const onChange = jest.fn();
    render(
      <AsyncSearchCrewMember
        value={[]}
        fetchQuery={fetchQuery}
        uniqueQueryKey="test-key"
        onChange={onChange}
      />,
    );
    // Focus input to open dropdown and trigger fetch
    const input = screen.getByPlaceholderText('Select user');
    fireEvent.focus(input);
    // Wait for dropdown to appear
    expect(await screen.findByText('Alice Smith')).toBeInTheDocument();
    expect(fetchQuery).toHaveBeenCalled();
  });

  it('calls onChange with selected and originalData', async () => {
    const fetchQuery = jest.fn().mockResolvedValue({
      options: mockOptions,
      originalData: mockOriginalData,
    });
    const onChange = jest.fn();
    render(
      <AsyncSearchCrewMember
        value={[]}
        fetchQuery={fetchQuery}
        uniqueQueryKey="test-key"
        onChange={onChange}
      />,
    );
    const input = screen.getByPlaceholderText('Select user');
    fireEvent.focus(input);
    const userButton = await screen.findByText('Alice Smith');
    fireEvent.click(userButton);
    expect(onChange).toHaveBeenCalledWith(['1'], mockOriginalData);
  });

  it('shows loading state when isLoading is true', async () => {
    // Mock useQuery to return isLoading
    jest.doMock('../../src/hooks/useQuery', () => ({
      useQuery: () => ({
        data: {options: [], originalData: []},
        isLoading: true,
      }),
    }));
    const {
      AsyncSearchCrewMember: AsyncSearchCrewMemberReloaded,
    } = require('../../src/components/SearchCrewMember');
    render(
      <AsyncSearchCrewMemberReloaded
        value={[]}
        fetchQuery={jest.fn()}
        uniqueQueryKey="test-key"
        onChange={jest.fn()}
      />,
    );
    const input = screen.getByPlaceholderText('Select user');
    expect(input).toBeDisabled();
  });

  it('shows clear button and calls onChange([]) if value is empty and clear is clicked', async () => {
    const fetchQuery = jest.fn().mockResolvedValue({
      options: mockOptions,
      originalData: mockOriginalData,
    });
    const onChange = jest.fn();
    render(
      <AsyncSearchCrewMember
        value={[]}
        fetchQuery={fetchQuery}
        uniqueQueryKey="test-key"
        onChange={onChange}
      />,
    );
    const input = screen.getByPlaceholderText('Select user');
    fireEvent.focus(input);
    const clearBtn = document.querySelector('.clear-icon button');
    expect(clearBtn).toBeInTheDocument();
    fireEvent.click(clearBtn!);
    expect(onChange).toHaveBeenCalledWith([], expect.anything());
  });

  it('shows async message if search is less than 3 chars', async () => {
    const fetchQuery = jest
      .fn()
      .mockResolvedValue({options: [], originalData: []});
    render(
      <AsyncSearchCrewMember
        value={[]}
        fetchQuery={fetchQuery}
        uniqueQueryKey="test-key"
        onChange={jest.fn()}
      />,
    );
    const input = screen.getByPlaceholderText('Select user');
    fireEvent.focus(input);
    fireEvent.change(input, {target: {value: 'ab'}});
    expect(
      await screen.findByText('Please enter at least 3 characters to search.'),
    ).toBeInTheDocument();
  });

  it('renders with a custom placeholder', async () => {
    const fetchQuery = jest.fn().mockResolvedValue({
      options: mockOptions,
      originalData: mockOriginalData,
    });
    render(
      <AsyncSearchCrewMember
        value={[]}
        fetchQuery={fetchQuery}
        uniqueQueryKey="test-key"
        onChange={jest.fn()}
        placeholder="Custom Placeholder"
      />,
    );
    expect(
      screen.getByPlaceholderText('Custom Placeholder'),
    ).toBeInTheDocument();
  });

  it('does not call onChange on blur if nothing is selected', async () => {
    const fetchQuery = jest.fn().mockResolvedValue({
      options: mockOptions,
      originalData: mockOriginalData,
    });
    const onChange = jest.fn();
    render(
      <AsyncSearchCrewMember
        value={[]}
        fetchQuery={fetchQuery}
        uniqueQueryKey="test-key"
        onChange={onChange}
      />,
    );
    const input = screen.getByPlaceholderText('Select user');
    fireEvent.focus(input);
    fireEvent.blur(input);
    expect(onChange).not.toHaveBeenCalled();
  });

  it('calls onChange([]) when clear is clicked even if already empty', async () => {
    const fetchQuery = jest.fn().mockResolvedValue({
      options: mockOptions,
      originalData: mockOriginalData,
    });
    const onChange = jest.fn();
    render(
      <AsyncSearchCrewMember
        value={[]}
        fetchQuery={fetchQuery}
        uniqueQueryKey="test-key"
        onChange={onChange}
      />,
    );
    const input = screen.getByPlaceholderText('Select user');
    fireEvent.focus(input);
    // Simulate clear button present even if value is empty
    const clearBtn = document.querySelector('.clear-icon button');
    if (clearBtn) {
      fireEvent.click(clearBtn);
      expect(onChange).toHaveBeenCalledWith([], expect.anything());
    }
  });

  it('shows "No users found." when fetch returns empty', async () => {
    const fetchQuery = jest
      .fn()
      .mockResolvedValue({options: [], originalData: []});
    render(
      <AsyncSearchCrewMember
        value={[]}
        fetchQuery={fetchQuery}
        uniqueQueryKey="test-key"
        onChange={jest.fn()}
      />,
    );
    const input = screen.getByPlaceholderText('Select user');
    fireEvent.focus(input);
    fireEvent.change(input, {target: {value: 'abc'}});
    expect(await screen.findByText('No users found.')).toBeInTheDocument();
  });
});
