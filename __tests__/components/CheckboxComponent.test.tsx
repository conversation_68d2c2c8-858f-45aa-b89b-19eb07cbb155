import {render, screen, fireEvent} from '@testing-library/react';
import CheckboxComponent from '../../src/components/CheckboxComponent';

jest.mock('../../src/utils/svgIcons', () => ({
  CheckFilled: () => <svg data-testid="check-filled" />,
  CheckUnFilled: () => <svg data-testid="check-unfilled" />,
}));

describe('CheckboxComponent', () => {
  const baseProps = {
    id: 'test-checkbox',
    label: 'Accept Terms',
    onChange: jest.fn(),
  };

  beforeEach(() => {
    baseProps.onChange.mockClear();
  });

  describe('Rendering', () => {
    it('renders label correctly', () => {
      render(<CheckboxComponent {...baseProps} checked={false} />);
      expect(screen.getByText('Accept Terms')).toBeInTheDocument();
    });

    it('renders CheckFilled icon when checked is true', () => {
      render(<CheckboxComponent {...baseProps} checked={true} />);
      expect(screen.getByTestId('check-filled')).toBeInTheDocument();
    });

    it('renders CheckUnFilled icon when checked is false', () => {
      render(<CheckboxComponent {...baseProps} checked={false} />);
      expect(screen.getByTestId('check-unfilled')).toBeInTheDocument();
    });

    it('renders without label when label is not provided', () => {
      const propsWithoutLabel = {...baseProps, label: undefined};
      render(<CheckboxComponent {...propsWithoutLabel} checked={false} />);

      expect(screen.queryByText('Accept Terms')).not.toBeInTheDocument();
      expect(screen.getByTestId('check-unfilled')).toBeInTheDocument();
    });

    it('renders with React node as label', () => {
      const reactNodeLabel = (
        <span>
          Custom <strong>Label</strong>
        </span>
      );
      const propsWithReactLabel = {...baseProps, label: reactNodeLabel};

      render(<CheckboxComponent {...propsWithReactLabel} checked={false} />);

      expect(screen.getByText('Custom')).toBeInTheDocument();
      expect(screen.getByText('Label')).toBeInTheDocument();
    });

    it('has correct CSS classes and structure', () => {
      render(<CheckboxComponent {...baseProps} checked={false} />);

      // Check that the container element has the correct class (it's a label, not div)
      const containerElement = document.querySelector(
        'label.ra-form-check-box',
      );
      expect(containerElement).toBeInTheDocument();
      expect(containerElement).toHaveClass('ra-form-check-box');

      // Check that the hidden input exists
      const hiddenInput = document.querySelector('input[type="checkbox"]');
      expect(hiddenInput).toBeInTheDocument();
      expect(hiddenInput).toHaveAttribute('id', 'test-checkbox');
    });

    it('contains hidden input element with correct attributes', () => {
      render(<CheckboxComponent {...baseProps} checked={true} />);

      const hiddenInput = document.querySelector('input[type="checkbox"]');
      expect(hiddenInput).toBeInTheDocument();
      expect(hiddenInput).toHaveAttribute('id', 'test-checkbox');
      expect(hiddenInput).toBeChecked();
      expect(hiddenInput).toHaveStyle({display: 'none'});
    });

    it('hidden input reflects unchecked state', () => {
      render(<CheckboxComponent {...baseProps} checked={false} />);

      const hiddenInput = document.querySelector('input[type="checkbox"]');
      expect(hiddenInput).toBeInTheDocument();
      expect(hiddenInput).not.toBeChecked();
    });
  });

  describe('Click Interactions', () => {
    it('calls onChange when label is clicked', () => {
      render(<CheckboxComponent {...baseProps} checked={false} />);

      const label = screen.getByLabelText('Accept Terms');
      fireEvent.click(label);

      expect(baseProps.onChange).toHaveBeenCalledTimes(1);
    });

    it('stops propagation on label click', () => {
      const parentClickHandler = jest.fn();

      render(
        <div onClick={parentClickHandler}>
          <CheckboxComponent {...baseProps} checked={false} />
        </div>,
      );

      const label = screen.getByLabelText('Accept Terms');
      fireEvent.click(label);

      expect(baseProps.onChange).toHaveBeenCalledTimes(1);
      expect(parentClickHandler).not.toHaveBeenCalled();
    });

    it('stops propagation on direct label element click', () => {
      const parentClickHandler = jest.fn();

      render(
        <div onClick={parentClickHandler}>
          <CheckboxComponent {...baseProps} checked={false} />
        </div>,
      );

      const labelElement = document.querySelector(
        'label.ra-form-check-box',
      ) as HTMLLabelElement;

      // Click the label element directly - this should trigger the onClick handler
      // which calls stopPropagation, preventing the parent click handler from being called
      fireEvent.click(labelElement);

      // The parent click handler should not be called due to stopPropagation
      expect(parentClickHandler).not.toHaveBeenCalled();
      // The onChange should still be called
      expect(baseProps.onChange).toHaveBeenCalledTimes(1);
    });

    it('stops propagation on hidden input click', () => {
      const parentClickHandler = jest.fn();

      render(
        <div onClick={parentClickHandler}>
          <CheckboxComponent {...baseProps} checked={false} />
        </div>,
      );

      const hiddenInput = document.querySelector(
        'input[type="checkbox"]',
      ) as HTMLInputElement;
      fireEvent.click(hiddenInput);

      expect(parentClickHandler).not.toHaveBeenCalled();
    });
  });

  describe('Keyboard Interactions', () => {
    it('calls onChange when Enter key is pressed on label', () => {
      render(<CheckboxComponent {...baseProps} checked={false} />);

      const label = screen.getByLabelText('Accept Terms');
      fireEvent.keyDown(label, {key: 'Enter'});

      expect(baseProps.onChange).toHaveBeenCalledTimes(1);
    });

    it('calls onChange when Space key is pressed on label', () => {
      render(<CheckboxComponent {...baseProps} checked={false} />);

      const label = screen.getByLabelText('Accept Terms');
      fireEvent.keyDown(label, {key: ' '});

      expect(baseProps.onChange).toHaveBeenCalledTimes(1);
    });

    it('does not call onChange when other keys are pressed on label', () => {
      render(<CheckboxComponent {...baseProps} checked={false} />);

      const label = screen.getByLabelText('Accept Terms');
      fireEvent.keyDown(label, {key: 'Tab'});
      fireEvent.keyDown(label, {key: 'Escape'});
      fireEvent.keyDown(label, {key: 'a'});
      fireEvent.keyDown(label, {key: 'ArrowDown'});

      expect(baseProps.onChange).not.toHaveBeenCalled();
    });

    it('does not call onChange when non-interactive keys are pressed on hidden input', () => {
      render(<CheckboxComponent {...baseProps} checked={false} />);

      const hiddenInput = document.querySelector(
        'input[type="checkbox"]',
      ) as HTMLInputElement;
      // Test keys that should not trigger change on a checkbox input
      fireEvent.keyDown(hiddenInput, {key: 'Tab'});
      fireEvent.keyDown(hiddenInput, {key: 'Escape'});
      fireEvent.keyDown(hiddenInput, {key: 'ArrowDown'});
      fireEvent.keyDown(hiddenInput, {key: 'a'});

      // Hidden input doesn't have keyDown handlers, only the label does
      expect(baseProps.onChange).not.toHaveBeenCalled();
    });

    it('stops propagation on input change events', () => {
      const parentChangeHandler = jest.fn();

      render(
        <div onChange={parentChangeHandler}>
          <CheckboxComponent {...baseProps} checked={false} />
        </div>,
      );

      const hiddenInput = document.querySelector(
        'input[type="checkbox"]',
      ) as HTMLInputElement;

      // Simulate user interaction that triggers change
      fireEvent.click(hiddenInput);

      expect(baseProps.onChange).toHaveBeenCalledTimes(1);
      expect(parentChangeHandler).not.toHaveBeenCalled();
    });
  });

  describe('Accessibility', () => {
    it('has correct accessibility attributes when checked', () => {
      render(<CheckboxComponent {...baseProps} checked={true} />);

      const hiddenInput = document.querySelector(
        'input[type="checkbox"]',
      ) as HTMLInputElement;
      expect(hiddenInput).toBeChecked();
      expect(hiddenInput).toHaveAttribute('type', 'checkbox');
      expect(hiddenInput).toHaveAttribute('id', 'test-checkbox');
    });

    it('has correct accessibility attributes when unchecked', () => {
      render(<CheckboxComponent {...baseProps} checked={false} />);

      const hiddenInput = document.querySelector(
        'input[type="checkbox"]',
      ) as HTMLInputElement;
      expect(hiddenInput).not.toBeChecked();
      expect(hiddenInput).toHaveAttribute('type', 'checkbox');
      expect(hiddenInput).toHaveAttribute('id', 'test-checkbox');
    });

    it('has correct label association', () => {
      render(<CheckboxComponent {...baseProps} checked={false} />);

      const label = screen.getByLabelText('Accept Terms');
      expect(label).toBeInTheDocument();

      const hiddenInput = document.querySelector(
        'input[type="checkbox"]',
      ) as HTMLInputElement;
      expect(hiddenInput).toHaveAttribute('id', 'test-checkbox');
    });

    it('label is properly associated with input', () => {
      render(<CheckboxComponent {...baseProps} checked={false} />);

      const labelElement = document.querySelector('label[for="test-checkbox"]');
      expect(labelElement).toBeInTheDocument();
      expect(labelElement).toHaveClass('ra-form-check-box');
    });

    it('works without label', () => {
      const propsWithoutLabel = {...baseProps, label: undefined};
      render(<CheckboxComponent {...propsWithoutLabel} checked={false} />);

      const hiddenInput = document.querySelector(
        'input[type="checkbox"]',
      ) as HTMLInputElement;
      expect(hiddenInput).toBeInTheDocument();
      expect(hiddenInput).toHaveAttribute('id', 'test-checkbox');
    });

    it('handles React node labels', () => {
      const reactNodeLabel = <span>Custom Label</span>;
      const propsWithReactLabel = {...baseProps, label: reactNodeLabel};

      render(<CheckboxComponent {...propsWithReactLabel} checked={false} />);

      expect(screen.getByText('Custom Label')).toBeInTheDocument();
      const hiddenInput = document.querySelector(
        'input[type="checkbox"]',
      ) as HTMLInputElement;
      expect(hiddenInput).toBeInTheDocument();
    });

    it('is focusable and accessible', async () => {
      render(<CheckboxComponent {...baseProps} checked={false} />);

      const hiddenInput = document.querySelector(
        'input[type="checkbox"]',
      ) as HTMLInputElement;

      // Focus the checkbox
      hiddenInput.focus();
      expect(hiddenInput).toHaveFocus();

      // Activate with click (simulating user interaction)
      fireEvent.click(hiddenInput);
      expect(baseProps.onChange).toHaveBeenCalledTimes(1);
    });
  });

  describe('Edge Cases', () => {
    it('handles multiple rapid clicks correctly', () => {
      render(<CheckboxComponent {...baseProps} checked={false} />);

      const label = screen.getByLabelText('Accept Terms');
      fireEvent.click(label);
      fireEvent.click(label);
      fireEvent.click(label);

      expect(baseProps.onChange).toHaveBeenCalledTimes(3);
    });

    it('handles multiple click events', () => {
      render(<CheckboxComponent {...baseProps} checked={false} />);

      const label = screen.getByLabelText('Accept Terms');
      const hiddenInput = document.querySelector(
        'input[type="checkbox"]',
      ) as HTMLInputElement;

      fireEvent.click(label);
      fireEvent.click(hiddenInput);

      expect(baseProps.onChange).toHaveBeenCalledTimes(2);
    });

    it('maintains visual consistency with label styling', () => {
      render(<CheckboxComponent {...baseProps} checked={false} />);

      const labelElement = document.querySelector(
        'label.ra-form-check-box',
      ) as HTMLLabelElement;

      // Check that it has the correct styling
      expect(labelElement.tagName).toBe('LABEL');
      expect(labelElement).toHaveStyle({
        color: '#333333',
        gap: '8px',
        userSelect: 'none',
        cursor: 'pointer',
      });
    });

    it('handles empty string label', () => {
      const propsWithEmptyLabel = {...baseProps, label: ''};
      render(<CheckboxComponent {...propsWithEmptyLabel} checked={false} />);

      const hiddenInput = document.querySelector(
        'input[type="checkbox"]',
      ) as HTMLInputElement;
      expect(hiddenInput).toBeInTheDocument();

      // Empty label should not render label text
      const labelText = document.querySelector('label span:last-child');
      expect(labelText).not.toBeInTheDocument();
    });

    it('handles special characters in id', () => {
      const propsWithSpecialId = {
        ...baseProps,
        id: 'test-checkbox-123_special',
      };
      render(<CheckboxComponent {...propsWithSpecialId} checked={false} />);

      const hiddenInput = document.querySelector('input[type="checkbox"]');
      expect(hiddenInput).toHaveAttribute('id', 'test-checkbox-123_special');

      const labelElement = document.querySelector(
        'label[for="test-checkbox-123_special"]',
      );
      expect(labelElement).toBeInTheDocument();
    });
  });

  describe('State Changes', () => {
    it('updates icon when checked state changes', () => {
      const {rerender} = render(
        <CheckboxComponent {...baseProps} checked={false} />,
      );

      expect(screen.getByTestId('check-unfilled')).toBeInTheDocument();
      expect(screen.queryByTestId('check-filled')).not.toBeInTheDocument();

      rerender(<CheckboxComponent {...baseProps} checked={true} />);

      expect(screen.getByTestId('check-filled')).toBeInTheDocument();
      expect(screen.queryByTestId('check-unfilled')).not.toBeInTheDocument();
    });

    it('updates checked state when checked prop changes', () => {
      const {rerender} = render(
        <CheckboxComponent {...baseProps} checked={false} />,
      );

      const hiddenInput = document.querySelector(
        'input[type="checkbox"]',
      ) as HTMLInputElement;
      expect(hiddenInput.checked).toBe(false);

      rerender(<CheckboxComponent {...baseProps} checked={true} />);

      expect(hiddenInput.checked).toBe(true);
    });

    it('updates hidden input checked state', () => {
      const {rerender} = render(
        <CheckboxComponent {...baseProps} checked={false} />,
      );

      const hiddenInput = document.querySelector(
        'input[type="checkbox"]',
      ) as HTMLInputElement;
      expect(hiddenInput.checked).toBe(false);

      rerender(<CheckboxComponent {...baseProps} checked={true} />);

      expect(hiddenInput.checked).toBe(true);
    });
  });
});
