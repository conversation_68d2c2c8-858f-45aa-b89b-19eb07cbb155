import React from 'react';
import {render, screen, fireEvent} from '@testing-library/react';
import {MemoryRouter} from 'react-router-dom';
import '@testing-library/jest-dom';
import ProjectBreadCrumb from '../../src/components/ProjectBreadCrumb';

describe('ProjectBreadCrumb', () => {
  const renderWithRouter = (ui: React.ReactElement) => {
    return render(ui, {wrapper: MemoryRouter});
  };

  it('renders breadcrumb without links when none are provided', () => {
    const items = [{title: 'Step One'}, {title: 'Step Two'}];

    renderWithRouter(<ProjectBreadCrumb items={items} />);

    items.forEach(item => {
      const element = screen.getByText(item.title);
      expect(element.tagName).toBe('SPAN');
    });

    expect(screen.queryByRole('link')).not.toBeInTheDocument();
  });

  it('applies correct text and link styles', () => {
    const items = [{title: 'Dashboard', link: '/dashboard'}];

    renderWithRouter(<ProjectBreadCrumb items={items} />);
    const link = screen.getByText('Dashboard');

    expect(link).toHaveStyle('color: #1F4A70');
    expect(link).toHaveClass('fs-24');
  });

  it('preserves state in the link if provided', () => {
    const state = {from: 'search'};
    const items = [{title: 'Search Results', link: '/search', state}];

    renderWithRouter(<ProjectBreadCrumb items={items} />);

    const link = screen.getByText('Search Results');
    expect(link).toBeInTheDocument();
    expect(link.closest('a')).toHaveAttribute('href', '/search');
  });

  it('renders correctly with only one item', () => {
    const items = [{title: 'Only Item'}];
    renderWithRouter(<ProjectBreadCrumb items={items} />);

    const text = screen.getByText('Only Item');
    expect(text).toBeInTheDocument();
    expect(text.tagName).toBe('SPAN');
    expect(screen.queryByText('/')).not.toBeInTheDocument();
  });

  // New test cases for improved coverage

  it('handles onClick events correctly', () => {
    const handleClick = jest.fn();
    const items = [
      {
        title: 'Clickable',
        link: '/click',
        onClick: handleClick,
      },
    ];

    renderWithRouter(<ProjectBreadCrumb items={items} />);
    const link = screen.getByText('Clickable');
    fireEvent.click(link);

    expect(handleClick).toHaveBeenCalledWith(expect.any(Object), '/click');
  });

  it('renders separator between multiple items correctly', () => {
    const items = [
      {title: 'First', link: '/first'},
      {title: 'Second', link: '/second'},
      {title: 'Third'},
    ];

    renderWithRouter(<ProjectBreadCrumb items={items} />);

    // Check for separators
    const separators = screen.getAllByText('/');
    expect(separators).toHaveLength(2); // Should have 2 separators for 3 items
  });

  it('renders custom options correctly', () => {
    const items = [{title: 'Main'}];
    const options = <button>Custom Option</button>;

    renderWithRouter(<ProjectBreadCrumb items={items} options={options} />);

    expect(screen.getByText('Main')).toBeInTheDocument();
    expect(screen.getByText('Custom Option')).toBeInTheDocument();
  });

  it('renders without options when not provided', () => {
    const items = [{title: 'Main'}];

    renderWithRouter(<ProjectBreadCrumb items={items} />);

    const container = screen.getByText('Main').closest('div');
    expect(container?.nextSibling).toBe(null);
  });

  it('handles items with both link and state correctly', () => {
    const state = {data: 'test'};
    const handleClick = jest.fn();
    const items = [
      {
        title: 'Complex Item',
        link: '/complex',
        state: state,
        onClick: handleClick,
      },
    ];

    renderWithRouter(<ProjectBreadCrumb items={items} />);

    const link = screen.getByText('Complex Item');
    fireEvent.click(link);

    expect(handleClick).toHaveBeenCalledWith(expect.any(Object), '/complex');
    expect(link.closest('a')).toHaveAttribute('href', '/complex');
  });
});
