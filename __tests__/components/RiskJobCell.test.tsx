import React from 'react';
import {render, screen} from '@testing-library/react';
import {RiskJobCell} from '../../src/components/RiskJobCell';

// Mock the JobAlertIcon
jest.mock('../../src/utils/svgIcons', () => ({
  JobAlertIcon: () => <div data-testid="job-alert-icon">Alert Icon</div>,
}));

describe('RiskJobCell', () => {
  const baseOriginal = {
    job_step: 'Test Job Step',
  };

  it('renders job step text', () => {
    render(<RiskJobCell type="risk" original={baseOriginal} />);

    expect(screen.getByText('Test Job Step')).toBeInTheDocument();
  });

  it('shows alert icon when risk type has residual risk rating with reason', () => {
    const originalWithRisk = {
      ...baseOriginal,
      risk_job_residual_risk_rating: [{reason: 'Some risk reason'}],
    };

    render(<RiskJobCell type="risk" original={originalWithRisk} />);

    expect(screen.getByTestId('job-alert-icon')).toBeInTheDocument();
    expect(screen.getByText('Test Job Step')).toBeInTheDocument();
  });

  it('shows alert icon when template type has residual risk rating with reason', () => {
    const originalWithRisk = {
      ...baseOriginal,
      template_job_residual_risk_rating: [
        {reason: 'Some template risk reason'},
      ],
    };

    render(<RiskJobCell type="template" original={originalWithRisk} />);

    expect(screen.getByTestId('job-alert-icon')).toBeInTheDocument();
    expect(screen.getByText('Test Job Step')).toBeInTheDocument();
  });

  it('does not show alert icon when no residual risk rating', () => {
    render(<RiskJobCell type="risk" original={baseOriginal} />);

    expect(screen.queryByTestId('job-alert-icon')).not.toBeInTheDocument();
    expect(screen.getByText('Test Job Step')).toBeInTheDocument();
  });

  it('does not show alert icon when residual risk rating has no reason', () => {
    const originalWithoutReason = {
      ...baseOriginal,
      risk_job_residual_risk_rating: [{reason: ''}],
    };

    render(<RiskJobCell type="risk" original={originalWithoutReason} />);

    expect(screen.queryByTestId('job-alert-icon')).not.toBeInTheDocument();
    expect(screen.getByText('Test Job Step')).toBeInTheDocument();
  });

  it('does not show alert icon when residual risk rating is empty array', () => {
    const originalWithEmptyArray = {
      ...baseOriginal,
      risk_job_residual_risk_rating: [],
    };

    render(<RiskJobCell type="risk" original={originalWithEmptyArray} />);

    expect(screen.queryByTestId('job-alert-icon')).not.toBeInTheDocument();
    expect(screen.getByText('Test Job Step')).toBeInTheDocument();
  });

  it('handles multiple risk ratings correctly', () => {
    const originalWithMultipleRisks = {
      ...baseOriginal,
      risk_job_residual_risk_rating: [
        {reason: ''},
        {reason: 'Valid reason'},
        {reason: ''},
      ],
    };

    render(<RiskJobCell type="risk" original={originalWithMultipleRisks} />);

    expect(screen.getByTestId('job-alert-icon')).toBeInTheDocument();
    expect(screen.getByText('Test Job Step')).toBeInTheDocument();
  });

  it('renders with correct CSS classes', () => {
    const {container} = render(
      <RiskJobCell type="risk" original={baseOriginal} />,
    );

    const mainDiv = container.firstChild;
    expect(mainDiv).toHaveClass('gap-3', 'd-flex', 'align-items-center');

    const iconContainer = container.querySelector(
      '.d-flex.align-items-center.mw-48',
    );
    expect(iconContainer).toBeInTheDocument();
  });

  it('handles undefined residual risk rating', () => {
    const originalWithUndefined = {
      ...baseOriginal,
      risk_job_residual_risk_rating: undefined,
    };

    render(<RiskJobCell type="risk" original={originalWithUndefined} />);

    expect(screen.queryByTestId('job-alert-icon')).not.toBeInTheDocument();
    expect(screen.getByText('Test Job Step')).toBeInTheDocument();
  });
});
