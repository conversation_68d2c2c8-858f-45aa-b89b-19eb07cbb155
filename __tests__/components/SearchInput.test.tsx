import React from 'react';
import {render, screen, fireEvent} from '@testing-library/react';
import SearchInput from '../../src/components/SearchInput';

describe('SearchInput', () => {
  const mockOnSearch = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders with custom placeholder', () => {
    render(
      <SearchInput
        value=""
        onSearch={mockOnSearch}
        placeholder="Custom Placeholder"
      />,
    );
    expect(
      screen.getByPlaceholderText('Custom Placeholder'),
    ).toBeInTheDocument();
  });

  it('calls onSearch with null when input is empty or whitespace only', () => {
    render(<SearchInput value="" onSearch={mockOnSearch} />);
    const input = screen.getByRole('textbox');

    fireEvent.change(input, {target: {value: '    '}});
    expect(mockOnSearch).toHaveBeenCalledWith(null);

    fireEvent.change(input, {target: {value: ''}});
    expect(mockOnSearch).toHaveBeenCalledWith(null);
  });

  it('calls onSearch with cleaned value (removes % and _)', () => {
    render(<SearchInput value="" onSearch={mockOnSearch} />);
    const input = screen.getByRole('textbox');

    fireEvent.change(input, {target: {value: 'abc%_def'}});
    expect(mockOnSearch).toHaveBeenCalledWith('abcdef');

    fireEvent.change(input, {target: {value: 'hello_world%'}});
    expect(mockOnSearch).toHaveBeenCalledWith('helloworld');
  });

  it('passes disabled prop to input', () => {
    render(<SearchInput value="" onSearch={mockOnSearch} disabled />);
    expect(screen.getByRole('textbox')).toBeDisabled();
  });

  it('reflects the controlled value prop', () => {
    const {rerender} = render(
      <SearchInput value="initial" onSearch={mockOnSearch} />,
    );
    const input = screen.getByRole('textbox');
    expect(input).toHaveValue('initial');

    rerender(<SearchInput value="updated" onSearch={mockOnSearch} />);
    expect(input).toHaveValue('updated');
  });

  it('shows clear button when showClear is true', () => {
    render(<SearchInput value="test" onSearch={mockOnSearch} showClear />);
    expect(screen.getByTestId('clear-icon')).toBeInTheDocument();
    expect(screen.getByTestId('cross-icon')).toBeInTheDocument();
  });

  it('does not show clear button when showClear is false', () => {
    render(
      <SearchInput value="test" onSearch={mockOnSearch} showClear={false} />,
    );
    expect(screen.queryByTestId('clear-icon')).not.toBeInTheDocument();
    expect(screen.queryByTestId('cross-icon')).not.toBeInTheDocument();
  });

  it('does not show clear button by default', () => {
    render(<SearchInput value="test" onSearch={mockOnSearch} />);
    expect(screen.queryByTestId('clear-icon')).not.toBeInTheDocument();
    expect(screen.queryByTestId('cross-icon')).not.toBeInTheDocument();
  });

  it('calls onSearch with empty string when clear button is clicked', () => {
    render(<SearchInput value="test" onSearch={mockOnSearch} showClear />);
    const clearButton = screen.getByTestId('cross-icon').closest('button');

    fireEvent.click(clearButton!);
    expect(mockOnSearch).toHaveBeenCalledWith('');
  });

  it('renders with default placeholder when not provided', () => {
    render(<SearchInput value="" onSearch={mockOnSearch} />);
    expect(screen.getByPlaceholderText('Search')).toBeInTheDocument();
  });

  it('renders search icon', () => {
    render(<SearchInput value="" onSearch={mockOnSearch} />);
    // The search icon should be present in the DOM
    const searchIconContainer = document.querySelector('.clear-icon');
    expect(searchIconContainer).toBeInTheDocument();
  });
});
