import React from 'react';
import {render, screen, fireEvent} from '@testing-library/react';
import CardGallery from '../../src/components/CardGallery';

const mockData = [
  {id: 1, name: 'Card 1'},
  {id: 2, name: 'Card 2'},
];

describe('CardGallery', () => {
  it('renders loading state', () => {
    render(
      <CardGallery data={[]} renderItem={() => <div>Item</div>} isLoading />,
    );
    expect(screen.getByText(/loading/i)).toBeInTheDocument();
  });

  it('renders empty state', () => {
    render(<CardGallery data={[]} renderItem={() => <div>Item</div>} />);
    expect(screen.getByText(/no items found/i)).toBeInTheDocument();
  });

  it('renders items', () => {
    render(
      <CardGallery
        data={mockData}
        renderItem={item => <div>{item.name}</div>}
      />,
    );
    expect(screen.getByText('Card 1')).toBeInTheDocument();
    expect(screen.getByText('Card 2')).toBeInTheDocument();
  });

  it('calls fetchNextPage on scroll near bottom', () => {
    const fetchNextPage = jest.fn();
    const pagination = {page: 1, totalPages: 2, pageSize: 10};
    const {container} = render(
      <CardGallery
        data={mockData}
        renderItem={item => <div>{item.name}</div>}
        fetchNextPage={fetchNextPage}
        pagination={pagination}
      />,
    );
    const gallery = container.querySelector('.card-gallery-container');
    if (gallery) {
      Object.defineProperty(gallery, 'scrollHeight', {
        value: 500,
        configurable: true,
      });
      Object.defineProperty(gallery, 'scrollTop', {
        value: 100,
        configurable: true,
      });
      Object.defineProperty(gallery, 'clientHeight', {
        value: 200,
        configurable: true,
      });
      fireEvent.scroll(gallery);
    }
    expect(fetchNextPage).not.toThrow;
  });

  it('shows spinner when fetching next page', () => {
    render(
      <CardGallery
        data={mockData}
        renderItem={item => <div>{item.name}</div>}
        isFetchingNextPage
      />,
    );
    expect(screen.getByTestId('spinner')).toBeInTheDocument();
  });
});
