import React from 'react';
import {render, screen, fireEvent} from '@testing-library/react';
import VesselAndOfficeDropdown from '../../src/components/VesselAndOfficeDropdown';

jest.mock('../../src/components/SearchInput', () => (props: any) => (
  <input
    data-testid="mock-search-input"
    value={props.value}
    onChange={e => props.onSearch(e.target.value)}
  />
));
jest.mock('../../src/components/CheckboxComponent', () => (props: any) => (
  <input
    type="checkbox"
    data-testid={props.id}
    checked={props.checked}
    onChange={props.onChange}
  />
));
jest.mock('../../src/components/SingleBadgePopover', () => (props: any) => (
  <span data-testid="mock-badge">{props.label}</span>
));

const groupedOptions = [
  {
    label: 'Vessels',
    data: [
      {id: 1, name: 'Vessel 1'},
      {id: 2, name: 'Vessel 2'},
    ],
  },
  {
    label: 'Offices',
    data: [
      {id: 10, name: 'Office 1'},
      {id: 20, name: 'Office 2'},
    ],
  },
];

describe('VesselAndOfficeDropdown', () => {
  it('renders placeholder when nothing selected', () => {
    render(
      <VesselAndOfficeDropdown
        value={null}
        options={groupedOptions}
        onChange={jest.fn()}
        placeholder="Pick Vessel/Office"
      />,
    );
    expect(screen.getByText('Pick Vessel/Office')).toBeInTheDocument();
  });

  it('renders selected vessel and office names', () => {
    render(
      <VesselAndOfficeDropdown
        value={{vessel_id: [1], office_id: [10]}}
        options={groupedOptions}
        onChange={jest.fn()}
      />,
    );
    expect(screen.getByText('Vessel 1')).toBeInTheDocument();
    expect(screen.getByText('+1 More')).toBeInTheDocument();
  });

  it('opens dropdown and filters options', () => {
    render(
      <VesselAndOfficeDropdown
        value={null}
        options={groupedOptions}
        onChange={jest.fn()}
      />,
    );
    fireEvent.click(screen.getByRole('combobox'));
    expect(screen.getByTestId('mock-search-input')).toBeInTheDocument();
    fireEvent.change(screen.getByTestId('mock-search-input'), {
      target: {value: 'Vessel 2'},
    });
    expect(screen.getByText('Vessel 2')).toBeInTheDocument();
    expect(screen.queryByText('Vessel 1')).not.toBeInTheDocument();
  });

  it('calls onChange when vessel is selected', () => {
    const onChange = jest.fn();
    render(
      <VesselAndOfficeDropdown
        value={null}
        options={groupedOptions}
        onChange={onChange}
      />,
    );
    fireEvent.click(screen.getByRole('combobox'));
    fireEvent.click(screen.getByText('Vessel 1'));
    expect(onChange).toHaveBeenCalled();
  });

  it('shows select all/clear all button', () => {
    render(
      <VesselAndOfficeDropdown
        value={null}
        options={groupedOptions}
        onChange={jest.fn()}
      />,
    );
    fireEvent.click(screen.getByRole('combobox'));
    expect(screen.getByText('Select all')).toBeInTheDocument();
  });

  it('shows "No options found." when search yields no results', () => {
    render(
      <VesselAndOfficeDropdown
        value={null}
        options={groupedOptions}
        onChange={jest.fn()}
      />,
    );
    fireEvent.click(screen.getByRole('combobox'));
    fireEvent.change(screen.getByTestId('mock-search-input'), {
      target: {value: 'nonexistent'},
    });
    expect(screen.getByText('No results found.')).toBeInTheDocument();
  });

  it('calls onChange for select all', () => {
    const onChange = jest.fn();
    render(
      <VesselAndOfficeDropdown
        value={null}
        options={groupedOptions}
        onChange={onChange}
      />,
    );
    fireEvent.click(screen.getByRole('combobox'));
    fireEvent.click(screen.getByText('Select all'));
    expect(onChange).toHaveBeenCalled();
  });

  it('calls onChange for clear all', () => {
    const onChange = jest.fn();
    render(
      <VesselAndOfficeDropdown
        value={{vessel_id: [1, 2], office_id: [10, 20]}}
        options={groupedOptions}
        onChange={onChange}
      />,
    );
    fireEvent.click(screen.getByRole('combobox'));
    fireEvent.click(screen.getByText('Clear all'));
    expect(onChange).toHaveBeenCalled();
  });

  it('handles empty options array', () => {
    render(
      <VesselAndOfficeDropdown
        value={null}
        options={[]}
        onChange={jest.fn()}
      />,
    );
    fireEvent.click(screen.getByRole('combobox'));
    expect(screen.getByText('No results found.')).toBeInTheDocument();
  });

  it('closes dropdown on outside click', () => {
    render(
      <VesselAndOfficeDropdown
        value={null}
        options={groupedOptions}
        onChange={jest.fn()}
      />,
    );
    fireEvent.click(screen.getByRole('combobox'));
    expect(screen.getByTestId('mock-search-input')).toBeInTheDocument();
    fireEvent.mouseDown(document);
    expect(screen.queryByTestId('mock-search-input')).not.toBeInTheDocument();
  });

  it('keyboard accessibility: Enter, Space, Escape', () => {
    render(
      <VesselAndOfficeDropdown
        value={null}
        options={groupedOptions}
        onChange={jest.fn()}
      />,
    );
    const combobox = screen.getByRole('combobox');
    fireEvent.keyDown(combobox, {key: 'Enter'});
    expect(screen.getByTestId('mock-search-input')).toBeInTheDocument();
    fireEvent.keyDown(combobox, {key: 'Escape'});
    expect(screen.queryByTestId('mock-search-input')).not.toBeInTheDocument();
    fireEvent.keyDown(combobox, {key: ' '});
    expect(screen.getByTestId('mock-search-input')).toBeInTheDocument();
  });

  it('has proper aria attributes', () => {
    render(
      <VesselAndOfficeDropdown
        value={null}
        options={groupedOptions}
        onChange={jest.fn()}
      />,
    );
    const combobox = screen.getByRole('combobox');
    expect(combobox).toHaveAttribute('aria-haspopup', 'listbox');
    expect(combobox).toHaveAttribute('aria-expanded');
    expect(combobox).toHaveAttribute('role', 'combobox');
  });

  it('shows counter and SingleBadgePopover when more than maxDisplayNames selected', () => {
    render(
      <VesselAndOfficeDropdown
        value={{vessel_id: [1, 2], office_id: [10, 20]}}
        options={groupedOptions}
        onChange={jest.fn()}
        maxDisplayNames={1}
      />,
    );
    expect(screen.getByText('+3 More')).toBeInTheDocument();
    expect(screen.getByTestId('mock-badge')).toBeInTheDocument();
  });

  it('handles grouped options and vessel/office selection', () => {
    const onChange = jest.fn();
    render(
      <VesselAndOfficeDropdown
        value={null}
        options={groupedOptions}
        onChange={onChange}
      />,
    );
    fireEvent.click(screen.getByRole('combobox'));
    // Vessel group
    expect(screen.getByText('Vessels')).toBeInTheDocument();
    expect(screen.getByText('Vessel 1')).toBeInTheDocument();
    // Office group
    expect(screen.getByText('Offices')).toBeInTheDocument();
    expect(screen.getByText('Office 1')).toBeInTheDocument();
    // Select vessel
    fireEvent.click(screen.getByText('Vessel 2'));
    expect(onChange).toHaveBeenCalled();
    // Select office
    fireEvent.click(screen.getByText('Office 2'));
    expect(onChange).toHaveBeenCalled();
  });

  it('renders empty group labels but not No options found if groups exist', () => {
    const groupedOptions = [
      {label: 'Group 1', data: []},
      {label: 'Group 2', data: []},
    ];
    render(
      <VesselAndOfficeDropdown
        value={null}
        options={groupedOptions}
        onChange={jest.fn()}
      />,
    );
    fireEvent.click(screen.getByRole('combobox'));
    expect(screen.getByText('Group 1')).toBeInTheDocument();
    expect(screen.getByText('Group 2')).toBeInTheDocument();
    expect(screen.queryByText('No results found.')).not.toBeInTheDocument();
  });

  it('does not call onChange in handleSelectAll when no options', () => {
    const onChange = jest.fn();
    render(
      <VesselAndOfficeDropdown value={null} options={[]} onChange={onChange} />,
    );
    fireEvent.click(screen.getByRole('combobox'));
    const btn = screen.queryByRole('button', {name: /select all|clear all/i});
    if (btn) {
      fireEvent.click(btn);
    }
    expect(onChange).not.toHaveBeenCalled();
  });

  it('calls onChange with null when last vessel is deselected', () => {
    const onChange = jest.fn();
    const {unmount} = render(
      <VesselAndOfficeDropdown
        value={{vessel_id: [1], office_id: null}}
        options={groupedOptions}
        onChange={onChange}
      />,
    );
    fireEvent.click(screen.getByRole('combobox'));
    fireEvent.click(screen.getByTestId('option-check-Vessels-1'));
    expect(onChange).toHaveBeenCalledWith({vessel_id: null, office_id: null});
    unmount();
  });

  it('calls onChange with null when last office is deselected', () => {
    const onChange = jest.fn();
    const {unmount} = render(
      <VesselAndOfficeDropdown
        value={{vessel_id: null, office_id: [10]}}
        options={groupedOptions}
        onChange={onChange}
      />,
    );
    fireEvent.click(screen.getByRole('combobox'));
    fireEvent.click(screen.getByTestId('option-check-Offices-10'));
    expect(onChange).toHaveBeenCalledWith({vessel_id: null, office_id: null});
    unmount();
  });

  it('toggles selection in onChange (add and remove vessel)', () => {
    const onChange = jest.fn();
    const {unmount} = render(
      <VesselAndOfficeDropdown
        value={{vessel_id: [], office_id: null}}
        options={groupedOptions}
        onChange={onChange}
      />,
    );
    fireEvent.click(screen.getByRole('combobox'));
    fireEvent.click(screen.getByTestId('option-check-Vessels-1'));
    expect(onChange).toHaveBeenCalledWith({vessel_id: [1], office_id: null});
    unmount();
    const {unmount: unmount2} = render(
      <VesselAndOfficeDropdown
        value={{vessel_id: [1], office_id: null}}
        options={groupedOptions}
        onChange={onChange}
      />,
    );
    fireEvent.click(screen.getByRole('combobox'));
    fireEvent.click(screen.getByTestId('option-check-Vessels-1'));
    expect(onChange).toHaveBeenCalledWith({vessel_id: null, office_id: null});
    unmount2();
  });

  it('toggles selection in onChange (add and remove office)', () => {
    const onChange = jest.fn();
    const {unmount} = render(
      <VesselAndOfficeDropdown
        value={{vessel_id: null, office_id: []}}
        options={groupedOptions}
        onChange={onChange}
      />,
    );
    fireEvent.click(screen.getByRole('combobox'));
    fireEvent.click(screen.getByTestId('option-check-Offices-10'));
    expect(onChange).toHaveBeenCalledWith({vessel_id: null, office_id: [10]});
    unmount();
    const {unmount: unmount2} = render(
      <VesselAndOfficeDropdown
        value={{vessel_id: null, office_id: [10]}}
        options={groupedOptions}
        onChange={onChange}
      />,
    );
    fireEvent.click(screen.getByRole('combobox'));
    fireEvent.click(screen.getByTestId('option-check-Offices-10'));
    expect(onChange).toHaveBeenCalledWith({vessel_id: null, office_id: null});
    unmount2();
  });

  it('does not close dropdown on outside click if dropdownRef is not set', () => {
    render(
      <VesselAndOfficeDropdown
        value={null}
        options={groupedOptions}
        onChange={jest.fn()}
      />,
    );
    // Do not open dropdown, so ref is not set
    fireEvent.mouseDown(document);
    expect(screen.getByRole('combobox')).toBeInTheDocument();
  });

  it('covers early return in useLayoutEffect when refs are not set', () => {
    render(
      <VesselAndOfficeDropdown
        value={null}
        options={groupedOptions}
        onChange={jest.fn()}
      />,
    );
    // No error should occur
    expect(screen.getByRole('combobox')).toBeInTheDocument();
  });

  it('covers setMaxDisplayNames logic when namesMeasureRef has no children', () => {
    render(
      <VesselAndOfficeDropdown
        value={null}
        options={groupedOptions}
        onChange={jest.fn()}
      />,
    );
    // No error should occur
    expect(screen.getByRole('combobox')).toBeInTheDocument();
  });
});
