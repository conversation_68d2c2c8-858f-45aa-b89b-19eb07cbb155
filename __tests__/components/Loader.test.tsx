import React from 'react';
import {render, screen} from '@testing-library/react';
import Loader from '../../src/components/Loader';

describe('Loader', () => {
  it('renders with default props', () => {
    render(<Loader />);

    const loader = screen.getByTestId('defaultLoader');
    expect(loader).toBeInTheDocument();
    expect(loader).toHaveClass('spinner-border', 'border-5', 'text-secondary');
  });

  it('renders with overlay loader class when isOverlayLoader is true', () => {
    const {container} = render(<Loader isOverlayLoader={true} />);

    const loaderContainer = container.firstChild;
    expect(loaderContainer).toHaveClass('overlay-loader');
  });

  it('renders without overlay loader class when isOverlayLoader is false', () => {
    const {container} = render(<Loader isOverlayLoader={false} />);

    const loaderContainer = container.firstChild;
    expect(loaderContainer).not.toHaveClass('overlay-loader');
  });

  it('applies custom className when provided', () => {
    const customClass = 'custom-loader-class';
    const {container} = render(<Loader className={customClass} />);

    const loaderContainer = container.firstChild;
    expect(loaderContainer).toHaveClass(customClass);
  });

  it('applies both custom className and overlay loader class', () => {
    const customClass = 'custom-loader-class';
    const {container} = render(
      <Loader className={customClass} isOverlayLoader={true} />,
    );

    const loaderContainer = container.firstChild;
    expect(loaderContainer).toHaveClass(customClass, 'overlay-loader');
  });

  it('has correct default classes', () => {
    const {container} = render(<Loader />);

    const loaderContainer = container.firstChild;
    expect(loaderContainer).toHaveClass(
      'w-full',
      'h-full',
      'flex-1',
      'd-flex',
      'justify-content-center',
      'align-items-center',
    );
  });
});
