import React from 'react';
import {render, screen, fireEvent, waitFor} from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import Popover from '../../src/components/Popover';

describe('Popover', () => {
  const defaultProps = {
    text: 'This is popover text',
    children: <button>Tri<PERSON></button>,
  };

  it('renders the trigger element', () => {
    render(<Popover {...defaultProps} />);

    const triggerButton = screen.getByRole('button', {name: '<PERSON><PERSON>'});
    expect(triggerButton).toBeInTheDocument();
  });

  it('shows popover on hover', async () => {
    render(<Popover {...defaultProps} />);

    const triggerButton = screen.getByRole('button', {name: 'Trigger <PERSON>'});

    // Hover over the trigger
    fireEvent.mouseEnter(triggerButton);

    // Wait for popover to appear
    await waitFor(() => {
      expect(screen.getByText('This is popover text')).toBeInTheDocument();
    });
  });

  it('shows popover on focus', async () => {
    render(<Popover {...defaultProps} />);

    const triggerButton = screen.getByRole('button', {name: 'Trigger Button'});

    // Focus the trigger
    fireEvent.focus(triggerButton);

    // Wait for popover to appear
    await waitFor(() => {
      expect(screen.getByText('This is popover text')).toBeInTheDocument();
    });
  });

  it('hides popover on mouse leave', async () => {
    render(<Popover {...defaultProps} />);

    const triggerButton = screen.getByRole('button', {name: 'Trigger Button'});

    // Show popover
    fireEvent.mouseEnter(triggerButton);
    await waitFor(() => {
      expect(screen.getByText('This is popover text')).toBeInTheDocument();
    });

    // Hide popover
    fireEvent.mouseLeave(triggerButton);
    await waitFor(() => {
      expect(
        screen.queryByText('This is popover text'),
      ).not.toBeInTheDocument();
    });
  });

  it('renders with correct popover classes', async () => {
    render(<Popover {...defaultProps} />);

    const triggerButton = screen.getByRole('button', {name: 'Trigger Button'});
    fireEvent.mouseEnter(triggerButton);

    await waitFor(() => {
      const popoverElement = document.querySelector('.ra-single-badge-popover');
      expect(popoverElement).toBeInTheDocument();

      const popoverBody = document.querySelector(
        '.ra-single-badge-popover-body',
      );
      expect(popoverBody).toBeInTheDocument();
    });
  });

  it('renders with different text content', async () => {
    const customText = 'Custom popover content';
    render(
      <Popover text={customText} children={<span>Custom Trigger</span>} />,
    );

    const triggerElement = screen.getByText('Custom Trigger');
    fireEvent.mouseEnter(triggerElement);

    await waitFor(() => {
      expect(screen.getByText(customText)).toBeInTheDocument();
    });
  });

  it('works with different child elements', async () => {
    render(
      <Popover text="Test text" children={<div>Custom Div Trigger</div>} />,
    );

    const triggerElement = screen.getByText('Custom Div Trigger');
    expect(triggerElement).toBeInTheDocument();

    fireEvent.mouseEnter(triggerElement);

    await waitFor(() => {
      expect(screen.getByText('Test text')).toBeInTheDocument();
    });
  });
});
