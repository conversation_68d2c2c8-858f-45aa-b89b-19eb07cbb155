import React from 'react';
import {render, screen} from '@testing-library/react';
import ColoredTile, {
  ColoredTileProps,
  ColoredTileTheme,
} from '../../src/components/ColoredTile';

describe('ColoredTile', () => {
  const defaultProps: ColoredTileProps = {
    text: 'Test Text',
    theme: 'red',
  };

  it('renders with default props', () => {
    render(<ColoredTile {...defaultProps} />);

    const tile = screen.getByText('Test Text');
    expect(tile).toBeInTheDocument();
    expect(tile).toHaveClass('colored-tile');
    expect(tile).toHaveClass('colored-tile-red');
  });

  it('renders with all theme variants', () => {
    const themes: ColoredTileTheme[] = ['red', 'green', 'yellow', 'blue'];

    themes.forEach(theme => {
      const {rerender} = render(
        <ColoredTile text={`${theme} text`} theme={theme} />,
      );

      const tile = screen.getByText(`${theme} text`);
      expect(tile).toBeInTheDocument();
      expect(tile).toHaveClass('colored-tile');
      expect(tile).toHaveClass(`colored-tile-${theme}`);

      rerender(<div />); // Clear for next iteration
    });
  });

  it('renders with custom className', () => {
    render(<ColoredTile {...defaultProps} className="custom-class" />);

    const tile = screen.getByText('Test Text');
    expect(tile).toHaveClass('colored-tile');
    expect(tile).toHaveClass('colored-tile-red');
    expect(tile).toHaveClass('custom-class');
  });

  it('renders with different text content', () => {
    const testTexts = [
      'Short',
      'A longer text content',
      '123',
      'Special chars: !@#$%',
    ];

    testTexts.forEach(text => {
      const {rerender} = render(<ColoredTile text={text} theme="blue" />);

      const tile = screen.getByText(text);
      expect(tile).toBeInTheDocument();
      expect(tile).toHaveClass('colored-tile-blue');

      rerender(<div />); // Clear for next iteration
    });
  });

  it('renders as span element', () => {
    render(<ColoredTile {...defaultProps} />);

    const tile = screen.getByText('Test Text');
    expect(tile.tagName).toBe('SPAN');
  });

  it('combines multiple class names correctly', () => {
    render(<ColoredTile text="Test" theme="green" className="class1 class2" />);

    const tile = screen.getByText('Test');
    expect(tile).toHaveClass('colored-tile');
    expect(tile).toHaveClass('colored-tile-green');
    expect(tile).toHaveClass('class1');
    expect(tile).toHaveClass('class2');
  });
});
