import React from 'react';
import {render, screen, fireEvent} from '@testing-library/react';
import {MemoryRouter, useNavigate} from 'react-router-dom';
import Link from '../../src/components/Link';

jest.mock('react-router-dom', () => {
  const actual = jest.requireActual('react-router-dom');
  return {
    ...actual,
    useNavigate: jest.fn(),
  };
});

describe('Link', () => {
  it('renders children and href', () => {
    render(
      <MemoryRouter>
        <Link href="/test">Test Link</Link>
      </MemoryRouter>,
    );
    const link = screen.getByText('Test Link');
    expect(link).toBeInTheDocument();
    expect(link).toHaveAttribute('href', '/test');
  });

  it('calls navigate on click', () => {
    const navigate = jest.fn();
    (useNavigate as jest.Mock).mockReturnValue(navigate);
    render(
      <MemoryRouter>
        <Link href="/test">Test Link</Link>
      </MemoryRouter>,
    );
    const link = screen.getByText('Test Link');
    fireEvent.click(link);
    expect(navigate).toHaveBeenCalledWith('/test');
  });
});
