import React from 'react';
import {render, screen, fireEvent, waitFor} from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import {ActionsDropdownCell} from '../../src/components/ActionsDropdownCell';

// Mock the ThreeDotsMenuIcon component
jest.mock('../../src/components/icons', () => ({
  ThreeDotsMenuIcon: () => <div data-testid="three-dots-icon">Three Dots</div>,
}));

describe('ActionsDropdownCell', () => {
  const mockSetEditStep = jest.fn();
  const mockSetEditModalTitle = jest.fn();
  const mockSetSelectedJobIdx = jest.fn();
  const mockSetIsEdit = jest.fn();
  const mockSetShowDeleteJobModal = jest.fn();

  const defaultProps = {
    jobId: 'test-job-123',
    setEditStep: mockSetEditStep,
    setEditModalTitle: mockSetEditModalTitle,
    setSelectedJobIdx: mockSetSelectedJobIdx,
    setIsEdit: mockSetIsEdit,
    setShowDeleteJobModal: mockSetShowDeleteJobModal,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  const openDropdown = async (container: HTMLElement) => {
    const dropdownToggle = container.querySelector('.dropdown-toggle');
    if (dropdownToggle) {
      fireEvent.click(dropdownToggle);
      await waitFor(() => {
        const dropdownMenu = container.querySelector('.dropdown-menu');
        expect(dropdownMenu).toBeInTheDocument();
      });
    }
  };

  describe('Component Rendering', () => {
    it('should render the dropdown component', () => {
      render(<ActionsDropdownCell {...defaultProps} />);

      // Check for dropdown container
      const dropdown = document.querySelector('.ra-three-dots-dropdown');
      expect(dropdown).toBeInTheDocument();
      expect(dropdown).toHaveClass(
        'd-flex',
        'align-items-center',
        'justify-content-center',
      );
    });

    it('should render the three dots icon', () => {
      render(<ActionsDropdownCell {...defaultProps} />);

      expect(screen.getByTestId('three-dots-icon')).toBeInTheDocument();
    });

    it('should render dropdown toggle with correct classes', () => {
      render(<ActionsDropdownCell {...defaultProps} />);

      const dropdownToggle = document.querySelector(
        '.dropdown-toggle-no-caret',
      );
      expect(dropdownToggle).toBeInTheDocument();
    });

    it('should render dropdown structure', () => {
      const {container} = render(<ActionsDropdownCell {...defaultProps} />);

      // Check for main dropdown container
      const dropdown = container.querySelector('.dropdown');
      expect(dropdown).toBeInTheDocument();

      // Check for dropdown toggle
      const dropdownToggle = container.querySelector('.dropdown-toggle');
      expect(dropdownToggle).toBeInTheDocument();
    });
  });

  describe('Dropdown Menu Items', () => {
    it('should render Edit and Delete menu items when dropdown is opened', async () => {
      const {container} = render(<ActionsDropdownCell {...defaultProps} />);

      // Open the dropdown
      await openDropdown(container);

      // Check for menu items
      await waitFor(() => {
        expect(screen.getByText('Edit')).toBeInTheDocument();
        expect(screen.getByText('Delete')).toBeInTheDocument();
      });
    });

    it('should render component without errors', () => {
      expect(() =>
        render(<ActionsDropdownCell {...defaultProps} />),
      ).not.toThrow();
    });
  });

  describe('Edit Action Handler', () => {
    it('should call all required functions when Edit is clicked', async () => {
      const {container} = render(<ActionsDropdownCell {...defaultProps} />);

      // Open the dropdown first
      await openDropdown(container);

      // Find and click the Edit dropdown item
      const editItem = await screen.findByText('Edit');
      fireEvent.click(editItem);

      expect(mockSetEditStep).toHaveBeenCalledWith(5);
      expect(mockSetEditModalTitle).toHaveBeenCalledWith('Edit Associated Job');
      expect(mockSetSelectedJobIdx).toHaveBeenCalledWith('test-job-123');
      expect(mockSetIsEdit).toHaveBeenCalledWith(true);
    });

    it('should call functions with correct parameters for different jobId', async () => {
      const propsWithDifferentJobId = {
        ...defaultProps,
        jobId: 'different-job-456',
      };

      const {container} = render(
        <ActionsDropdownCell {...propsWithDifferentJobId} />,
      );

      // Open the dropdown first
      await openDropdown(container);

      const editItem = await screen.findByText('Edit');
      fireEvent.click(editItem);

      expect(mockSetEditStep).toHaveBeenCalledWith(5);
      expect(mockSetEditModalTitle).toHaveBeenCalledWith('Edit Associated Job');
      expect(mockSetSelectedJobIdx).toHaveBeenCalledWith('different-job-456');
      expect(mockSetIsEdit).toHaveBeenCalledWith(true);
    });
  });

  describe('Delete Action Handler', () => {
    it('should call required functions when Delete is clicked', async () => {
      const {container} = render(<ActionsDropdownCell {...defaultProps} />);

      // Open the dropdown first
      await openDropdown(container);

      const deleteItem = await screen.findByText('Delete');
      fireEvent.click(deleteItem);

      expect(mockSetSelectedJobIdx).toHaveBeenCalledWith('test-job-123');
      expect(mockSetShowDeleteJobModal).toHaveBeenCalledWith(true);
    });

    it('should call functions with correct jobId for different job', async () => {
      const propsWithDifferentJobId = {
        ...defaultProps,
        jobId: 'another-job-789',
      };

      const {container} = render(
        <ActionsDropdownCell {...propsWithDifferentJobId} />,
      );

      // Open the dropdown first
      await openDropdown(container);

      const deleteItem = await screen.findByText('Delete');
      fireEvent.click(deleteItem);

      expect(mockSetSelectedJobIdx).toHaveBeenCalledWith('another-job-789');
      expect(mockSetShowDeleteJobModal).toHaveBeenCalledWith(true);
    });
  });

  describe('Props Validation', () => {
    it('should handle empty jobId', async () => {
      const propsWithEmptyJobId = {
        ...defaultProps,
        jobId: '',
      };

      const {container} = render(
        <ActionsDropdownCell {...propsWithEmptyJobId} />,
      );

      // Open the dropdown first
      await openDropdown(container);

      const editItem = await screen.findByText('Edit');
      fireEvent.click(editItem);

      expect(mockSetSelectedJobIdx).toHaveBeenCalledWith('');
    });

    it('should render without crashing when all props are provided', () => {
      expect(() => {
        render(<ActionsDropdownCell {...defaultProps} />);
      }).not.toThrow();
    });
  });

  describe('Component Structure', () => {
    it('should have correct dropdown structure', () => {
      const {container} = render(<ActionsDropdownCell {...defaultProps} />);

      // Check for main dropdown container
      const dropdown = container.querySelector('.dropdown');
      expect(dropdown).toBeInTheDocument();

      // Check for dropdown toggle
      const dropdownToggle = container.querySelector('.dropdown-toggle');
      expect(dropdownToggle).toBeInTheDocument();
    });

    it('should have proper CSS classes applied', () => {
      const {container} = render(<ActionsDropdownCell {...defaultProps} />);

      const dropdownContainer = container.querySelector(
        '.ra-three-dots-dropdown',
      );
      expect(dropdownContainer).toHaveClass('d-flex');
      expect(dropdownContainer).toHaveClass('align-items-center');
      expect(dropdownContainer).toHaveClass('justify-content-center');
    });

    it('should have dropdown menu with correct classes when opened', async () => {
      const {container} = render(<ActionsDropdownCell {...defaultProps} />);

      // Open the dropdown
      await openDropdown(container);

      await waitFor(() => {
        const dropdownMenu = container.querySelector('.dropdown-menu');
        expect(dropdownMenu).toBeInTheDocument();
        expect(dropdownMenu).toHaveClass('dropdown-menu-right');
        expect(dropdownMenu).toHaveClass('text-style');
      });
    });
  });

  describe('Accessibility', () => {
    it('should have proper dropdown structure for screen readers', () => {
      const {container} = render(<ActionsDropdownCell {...defaultProps} />);

      const dropdownToggle = container.querySelector('.dropdown-toggle');
      expect(dropdownToggle).toBeInTheDocument();
      expect(dropdownToggle).toHaveAttribute('aria-expanded');
    });

    it('should render menu items with proper text content when opened', async () => {
      const {container} = render(<ActionsDropdownCell {...defaultProps} />);

      // Open the dropdown first
      await openDropdown(container);

      await waitFor(() => {
        expect(screen.getByText('Edit')).toBeInTheDocument();
        expect(screen.getByText('Delete')).toBeInTheDocument();
      });
    });
  });

  describe('Dropdown Interaction', () => {
    it('should toggle dropdown when clicked', async () => {
      const {container} = render(<ActionsDropdownCell {...defaultProps} />);

      const dropdownToggle = container.querySelector('.dropdown-toggle');
      expect(dropdownToggle).toHaveAttribute('aria-expanded', 'false');

      // Click to open
      fireEvent.click(dropdownToggle!);

      await waitFor(() => {
        expect(dropdownToggle).toHaveAttribute('aria-expanded', 'true');
      });
    });

    it('should have proper popper configuration', () => {
      const {container} = render(<ActionsDropdownCell {...defaultProps} />);

      // Component should render without errors, indicating proper popper config
      expect(container.querySelector('.dropdown')).toBeInTheDocument();
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('should handle null or undefined jobId gracefully', async () => {
      const propsWithNullJobId = {
        ...defaultProps,
        jobId: null as any,
      };

      expect(() => {
        render(<ActionsDropdownCell {...propsWithNullJobId} />);
      }).not.toThrow();
    });

    it('should handle special characters in jobId', async () => {
      const propsWithSpecialJobId = {
        ...defaultProps,
        jobId: 'job-123!@#$%^&*()',
      };

      const {container} = render(
        <ActionsDropdownCell {...propsWithSpecialJobId} />,
      );

      await openDropdown(container);

      const editItem = await screen.findByText('Edit');
      fireEvent.click(editItem);

      expect(mockSetSelectedJobIdx).toHaveBeenCalledWith('job-123!@#$%^&*()');
    });

    it('should maintain component state after multiple interactions', async () => {
      const {container} = render(<ActionsDropdownCell {...defaultProps} />);

      // Open and close dropdown multiple times
      const dropdownToggle = container.querySelector('.dropdown-toggle');

      // First interaction
      fireEvent.click(dropdownToggle!);
      await waitFor(() => {
        expect(dropdownToggle).toHaveAttribute('aria-expanded', 'true');
      });

      // Close dropdown (click outside or escape)
      fireEvent.click(document.body);

      // Second interaction
      fireEvent.click(dropdownToggle!);
      await waitFor(() => {
        expect(dropdownToggle).toHaveAttribute('aria-expanded', 'true');
      });

      // Component should still be functional
      const editItem = await screen.findByText('Edit');
      fireEvent.click(editItem);

      expect(mockSetEditStep).toHaveBeenCalledWith(5);
    });
  });

  describe('Performance and Optimization', () => {
    it('should not re-render unnecessarily when props do not change', () => {
      const {rerender} = render(<ActionsDropdownCell {...defaultProps} />);

      // Re-render with same props
      rerender(<ActionsDropdownCell {...defaultProps} />);

      // Component should render without issues
      expect(screen.getByTestId('three-dots-icon')).toBeInTheDocument();
    });

    it('should handle rapid successive clicks gracefully', async () => {
      const {container} = render(<ActionsDropdownCell {...defaultProps} />);

      await openDropdown(container);

      const editItem = await screen.findByText('Edit');

      // Rapid clicks
      fireEvent.click(editItem);
      fireEvent.click(editItem);
      fireEvent.click(editItem);

      // Should only call the handlers once per click
      expect(mockSetEditStep).toHaveBeenCalledTimes(3);
      expect(mockSetEditModalTitle).toHaveBeenCalledTimes(3);
      expect(mockSetSelectedJobIdx).toHaveBeenCalledTimes(3);
      expect(mockSetIsEdit).toHaveBeenCalledTimes(3);
    });
  });
});
