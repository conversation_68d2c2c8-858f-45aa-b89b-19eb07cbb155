import React from 'react';
import {render, screen} from '@testing-library/react';
import LevelOfRATag from '../../src/components/LevelOfRATag';

describe('LevelOfRATag', () => {
  it('renders correctly with default content', () => {
    render(<LevelOfRATag />);

    // Check for the label text
    expect(screen.getByText('• Level of RA:')).toBeInTheDocument();

    // Check for the colored tile text
    expect(screen.getByText('Level 1 RA')).toBeInTheDocument();
  });

  it('renders with correct structure and classes', () => {
    const {container} = render(<LevelOfRATag />);

    // Check main container
    const mainSpan = container.querySelector('span.d-flex.align-items-center');
    expect(mainSpan).toBeInTheDocument();

    // Check label div
    const labelDiv = container.querySelector('div.text-muted.fs-14.ml-2');
    expect(labelDiv).toBeInTheDocument();
    expect(labelDiv).toHaveTextContent('• Level of RA:');

    // Check colored tile container
    const tileContainer = container.querySelector(
      'div.ml-2.d-flex.align-items-center',
    );
    expect(tileContainer).toBeInTheDocument();
  });

  it('renders ColoredTile with correct props', () => {
    render(<LevelOfRATag />);

    const coloredTile = screen.getByText('Level 1 RA');
    expect(coloredTile).toBeInTheDocument();
    expect(coloredTile).toHaveClass('colored-tile');
    expect(coloredTile).toHaveClass('colored-tile-red');
  });

  it('has correct layout structure', () => {
    const {container} = render(<LevelOfRATag />);

    // Verify the nested structure
    const outerSpan = container.firstChild;
    expect(outerSpan).toHaveClass('d-flex', 'align-items-center');

    const children = outerSpan?.childNodes;
    expect(children).toHaveLength(2);

    // First child should be the label
    const labelDiv = children?.[0] as HTMLElement;
    expect(labelDiv).toHaveClass('text-muted', 'fs-14', 'ml-2');

    // Second child should be the tile container
    const tileContainer = children?.[1] as HTMLElement;
    expect(tileContainer).toHaveClass('ml-2', 'd-flex', 'align-items-center');
  });
});
