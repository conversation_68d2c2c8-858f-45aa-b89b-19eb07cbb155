import React from 'react';
import {render, screen} from '@testing-library/react';
import {UsernameProfile} from '../../src/components/UsernameProfile';

// Mock the getInitials function
jest.mock('../../src/components/SearchUserDropdown', () => ({
  getInitials: jest.fn((name: string) => {
    if (!name) return '';
    const words = name.trim().split(/\s+/);
    if (words.length === 1) {
      return words[0].substring(0, 2).toUpperCase();
    }
    return words
      .slice(0, 2)
      .map(word => word[0])
      .join('')
      .toUpperCase();
  }),
}));

describe('UsernameProfile', () => {
  const defaultProps = {
    username: '<PERSON>',
    subText: 'Software Engineer',
  };

  it('renders username and subtext correctly', () => {
    render(<UsernameProfile {...defaultProps} />);

    expect(screen.getByText('<PERSON>')).toBeInTheDocument();
    expect(screen.getByText('Software Engineer')).toBeInTheDocument();
  });

  it('displays initials in avatar', () => {
    render(<UsernameProfile {...defaultProps} />);

    const avatar = document.querySelector('.avatar');
    expect(avatar).toBeInTheDocument();
    expect(avatar).toHaveTextContent('JD');
  });

  it('renders with correct CSS classes', () => {
    const {container} = render(<UsernameProfile {...defaultProps} />);

    expect(container.querySelector('.user-profile-info')).toBeInTheDocument();
    expect(container.querySelector('.avatar')).toBeInTheDocument();
    expect(container.querySelector('.user-info')).toBeInTheDocument();
    expect(container.querySelector('.user-name')).toBeInTheDocument();
    expect(container.querySelector('.user-details')).toBeInTheDocument();
  });

  it('handles single name correctly', () => {
    render(<UsernameProfile username="Alice" subText="Designer" />);

    expect(screen.getByText('Alice')).toBeInTheDocument();
    expect(screen.getByText('Designer')).toBeInTheDocument();

    const avatar = document.querySelector('.avatar');
    expect(avatar).toHaveTextContent('AL');
  });

  it('handles empty username', () => {
    const {container} = render(
      <UsernameProfile username="" subText="No Name" />,
    );

    const userNameElement = container.querySelector('.user-name');
    expect(userNameElement).toBeInTheDocument();
    expect(userNameElement).toHaveTextContent('');
    expect(screen.getByText('No Name')).toBeInTheDocument();

    const avatar = document.querySelector('.avatar');
    expect(avatar).toHaveTextContent('');
  });

  it('handles empty subText', () => {
    const {container} = render(
      <UsernameProfile username="Bob Smith" subText="" />,
    );

    expect(screen.getByText('Bob Smith')).toBeInTheDocument();

    const userDetailsElement = container.querySelector('.user-details');
    expect(userDetailsElement).toBeInTheDocument();
    expect(userDetailsElement).toHaveTextContent('');

    const avatar = document.querySelector('.avatar');
    expect(avatar).toHaveTextContent('BS');
  });

  it('handles long names correctly', () => {
    render(
      <UsernameProfile
        username="Alexander Christopher Johnson"
        subText="Senior Software Engineer"
      />,
    );

    expect(
      screen.getByText('Alexander Christopher Johnson'),
    ).toBeInTheDocument();
    expect(screen.getByText('Senior Software Engineer')).toBeInTheDocument();

    const avatar = document.querySelector('.avatar');
    expect(avatar).toHaveTextContent('AC');
  });
});
