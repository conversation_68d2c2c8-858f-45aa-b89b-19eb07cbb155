import React from 'react';
import {render, screen, fireEvent, waitFor, act} from '@testing-library/react';
import useInfiniteQuery from '../../src/hooks/useInfiniteQuery';

// Mock fetch function
const mockApiResponse = (page = 1, totalPages = 2) => ({
  data: [`item${page}`],
  pagination: {
    totalItems: 2,
    totalPages,
    page,
    pageSize: 1,
  },
});

describe('useInfiniteQuery', () => {
  function TestComponent({fetchFn, options}: any) {
    const {
      data,
      isLoading,
      isFetchingNextPage,
      fetchNextPage,
      hasNextPage,
      error,
      refetch,
      reset,
    } = useInfiniteQuery(fetchFn, options);

    return (
      <div>
        <div data-testid="loading">{isLoading ? 'loading' : 'not-loading'}</div>
        <div data-testid="fetching-next">
          {isFetchingNextPage ? 'fetching' : 'not-fetching'}
        </div>
        <div data-testid="error">{error ? error.message : ''}</div>
        <div data-testid="items">{data.data ? data.data.join(',') : ''}</div>
        <div data-testid="page">{data.pagination.page}</div>
        <div data-testid="has-next">{hasNextPage ? 'yes' : 'no'}</div>
        <button onClick={fetchNextPage}>Next</button>
        <button onClick={refetch}>Refetch</button>
        <button onClick={reset}>Reset</button>
      </div>
    );
  }

  it('fetches initial data and shows loading state', async () => {
    const fetchFn = jest.fn().mockResolvedValue(mockApiResponse(1));

    await act(async () => {
      render(<TestComponent fetchFn={fetchFn} options={{page: 1, limit: 1}} />);
    });

    await waitFor(() => {
      expect(screen.getByTestId('loading')).toHaveTextContent('not-loading');
      expect(screen.getByTestId('items')).toHaveTextContent('item1');
      expect(screen.getByTestId('page')).toHaveTextContent('1');
      expect(screen.getByTestId('has-next')).toHaveTextContent('yes');
    });
  });

  it('fetches next page and appends data', async () => {
    const fetchFn = jest
      .fn()
      .mockImplementation(({page}) => Promise.resolve(mockApiResponse(page)));

    await act(async () => {
      render(<TestComponent fetchFn={fetchFn} options={{page: 1, limit: 1}} />);
    });

    await waitFor(() => screen.getByTestId('items'));

    await act(async () => {
      fireEvent.click(screen.getByText('Next'));
    });

    await waitFor(() => {
      expect(screen.getByTestId('fetching-next')).toHaveTextContent(
        'not-fetching',
      );
      expect(screen.getByTestId('items')).toHaveTextContent('item1,item2');
      expect(screen.getByTestId('page')).toHaveTextContent('2');
      expect(screen.getByTestId('has-next')).toHaveTextContent('no');
    });
  });

  it('handles errors on fetch', async () => {
    const fetchFn = jest.fn().mockRejectedValue(new Error('fail'));

    await act(async () => {
      render(<TestComponent fetchFn={fetchFn} options={{page: 1, limit: 1}} />);
    });

    await waitFor(() => {
      expect(screen.getByTestId('error')).toHaveTextContent('fail');
    });
  });

  it('refetches data when refetch is called', async () => {
    const fetchFn = jest
      .fn()
      .mockResolvedValueOnce(mockApiResponse(1))
      .mockResolvedValueOnce(mockApiResponse(1));

    await act(async () => {
      render(<TestComponent fetchFn={fetchFn} options={{page: 1, limit: 1}} />);
    });

    await waitFor(() => screen.getByTestId('items'));

    await act(async () => {
      fireEvent.click(screen.getByText('Refetch'));
    });

    await waitFor(() => {
      expect(fetchFn).toHaveBeenCalledTimes(2);
    });
  });

  it('resets to initial page and refetches data when reset is called', async () => {
    const fetchFn = jest.fn().mockResolvedValue(mockApiResponse(1));

    await act(async () => {
      render(<TestComponent fetchFn={fetchFn} options={{page: 1, limit: 1}} />);
    });

    await waitFor(() => screen.getByTestId('items'));

    await act(async () => {
      fireEvent.click(screen.getByText('Reset'));
    });

    await waitFor(() => {
      expect(fetchFn).toHaveBeenCalledTimes(2);
      expect(screen.getByTestId('page')).toHaveTextContent('1');
    });
  });

  it('handles no next page from the start', async () => {
    const fetchFn = jest.fn().mockResolvedValue(mockApiResponse(1, 1));

    await act(async () => {
      render(<TestComponent fetchFn={fetchFn} options={{page: 1, limit: 1}} />);
    });

    await waitFor(() => {
      expect(screen.getByTestId('has-next')).toHaveTextContent('no');
    });
  });

  it('prevents fetching next page if already fetching', async () => {
    let resolver: any;
    const fetchPromise = new Promise(resolve => (resolver = resolve));
    const fetchFn = jest
      .fn()
      .mockResolvedValueOnce(mockApiResponse(1)) // Initial call
      .mockReturnValueOnce(fetchPromise); // First next page call (pending)

    await act(async () => {
      render(<TestComponent fetchFn={fetchFn} options={{page: 1, limit: 1}} />);
    });

    await waitFor(() => screen.getByTestId('items'));

    // Click next twice rapidly
    fireEvent.click(screen.getByText('Next')); // triggers fetchNextPage
    fireEvent.click(screen.getByText('Next')); // should not trigger again

    // Wait a bit to ensure the second click is processed
    await new Promise(resolve => setTimeout(resolve, 10));

    expect(fetchFn).toHaveBeenCalledTimes(2); // one initial + one next page

    await act(async () => {
      resolver(mockApiResponse(2, 2)); // resolve pending promise
    });
  });

  it('prevents fetching next page if hasNextPage is false', async () => {
    const fetchFn = jest
      .fn()
      .mockResolvedValueOnce(mockApiResponse(1, 1)) // no next page
      .mockResolvedValueOnce(mockApiResponse(2, 2)); // should not be called

    await act(async () => {
      render(<TestComponent fetchFn={fetchFn} options={{page: 1, limit: 1}} />);
    });

    await waitFor(() => screen.getByTestId('items'));

    await act(async () => {
      fireEvent.click(screen.getByText('Next')); // should not call again
    });

    await waitFor(() => {
      expect(fetchFn).toHaveBeenCalledTimes(1); // next page should not trigger
    });
  });

  it('resets and refetches when options change after mount', async () => {
    const fetchFn = jest.fn().mockResolvedValue(mockApiResponse(1));

    const {rerender} = await act(async () => {
      return render(
        <TestComponent
          fetchFn={fetchFn}
          options={{page: 1, limit: 1, query: 'a'}}
        />,
      );
    });

    await waitFor(() => screen.getByTestId('items'));

    await act(async () => {
      rerender(
        <TestComponent
          fetchFn={fetchFn}
          options={{page: 1, limit: 1, query: 'b'}}
        />,
      );
    });

    await waitFor(() => {
      expect(fetchFn).toHaveBeenCalledTimes(2); // initial + reset on param change
    });
  });

  it('handles empty response data gracefully', async () => {
    const fetchFn = jest.fn().mockResolvedValue({
      data: [],
      pagination: {totalItems: 0, totalPages: 0, page: 1, pageSize: 1},
    });

    await act(async () => {
      render(<TestComponent fetchFn={fetchFn} options={{page: 1, limit: 1}} />);
    });

    await waitFor(() => {
      expect(screen.getByTestId('items')).toHaveTextContent('');
      expect(screen.getByTestId('has-next')).toHaveTextContent('no');
    });
  });

  it('resets state and data correctly', async () => {
    const fetchFn = jest.fn().mockImplementation(({page}) => {
      if (page === 1) {
        return Promise.resolve(mockApiResponse(1));
      }
      return Promise.resolve(mockApiResponse(2));
    });

    await act(async () => {
      render(<TestComponent fetchFn={fetchFn} options={{page: 1, limit: 1}} />);
    });

    await waitFor(() => screen.getByTestId('items'));
    expect(screen.getByTestId('items')).toHaveTextContent('item1');

    await act(async () => {
      fireEvent.click(screen.getByText('Next'));
    });

    await waitFor(() => {
      expect(screen.getByTestId('items')).toHaveTextContent('item1,item2');
    });

    await act(async () => {
      fireEvent.click(screen.getByText('Reset'));
    });

    await waitFor(() => {
      expect(screen.getByTestId('page')).toHaveTextContent('1');
      expect(screen.getByTestId('items')).toHaveTextContent('item1');
    });
  });

  it('does not fetch next page when hasNextPage is false', async () => {
    const fetchFn = jest.fn().mockResolvedValueOnce({
      data: ['item1'],
      pagination: {
        totalItems: 1,
        totalPages: 1,
        page: 1,
        pageSize: 1,
      },
    });

    await act(async () => {
      render(<TestComponent fetchFn={fetchFn} options={{page: 1, limit: 1}} />);
    });

    await waitFor(() => screen.getByTestId('items'));

    await act(async () => {
      fireEvent.click(screen.getByText('Next'));
    });

    await waitFor(() => {
      expect(fetchFn).toHaveBeenCalledTimes(1); // no next page
    });
  });

  it('does not fetch next page if already fetching', async () => {
    const fetchFn = jest
      .fn()
      .mockResolvedValueOnce(mockApiResponse(1)) // Initial call
      .mockImplementation(({page}) => {
        return new Promise(resolve =>
          setTimeout(() => resolve(mockApiResponse(page)), 100),
        );
      });

    await act(async () => {
      render(<TestComponent fetchFn={fetchFn} options={{page: 1, limit: 1}} />);
    });

    await waitFor(() => screen.getByTestId('items'));

    // Click next twice rapidly - only first should trigger
    fireEvent.click(screen.getByText('Next'));
    fireEvent.click(screen.getByText('Next')); // second click while first is pending

    // Wait for any potential async operations
    await new Promise(resolve => setTimeout(resolve, 50));

    await waitFor(() => {
      expect(fetchFn).toHaveBeenCalledTimes(2); // 1 for initial + 1 for next
    });
  });

  it('uses default page and limit values when options are not provided', async () => {
    const fetchFn = jest.fn().mockResolvedValue({
      data: ['item1'],
      pagination: {
        totalItems: 1,
        totalPages: 1,
        page: 1, // <-- fix here
        pageSize: 50,
      },
    });

    // Don't pass any options — rely on defaults
    await act(async () => {
      render(<TestComponent fetchFn={fetchFn} />);
    });

    await waitFor(() => screen.getByTestId('items'));

    expect(fetchFn).toHaveBeenCalledWith(
      expect.objectContaining({
        page: 1,
        limit: 50,
      }),
    );

    expect(screen.getByTestId('page')).toHaveTextContent('1');
    expect(screen.getByTestId('items')).toHaveTextContent('item1');
  });

  it('handles fetch error and sets error state', async () => {
    const errorMessage = 'Failed to fetch';
    const fetchFn = jest.fn().mockRejectedValue(new Error(errorMessage));

    await act(async () => {
      render(<TestComponent fetchFn={fetchFn} />);
    });

    await waitFor(() => {
      const errorDiv = screen.getByTestId('error');
      expect(errorDiv).toHaveTextContent(errorMessage);
    });
  });
});
