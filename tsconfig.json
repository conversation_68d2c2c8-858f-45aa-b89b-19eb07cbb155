{"extends": "ts-config-single-spa", "compilerOptions": {"target": "ES2018", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "react-jsx", "downlevelIteration": true, "types": ["jest", "node"]}, "files": ["src/paris2-risk-assessment.tsx"], "include": ["src/**/*"], "exclude": ["src/**/*.test*", "__tests__/**/*.test.tsx", "__tests__/**/*.test.ts"]}